# TCP Qt5 Docker 开发环境

这个Docker环境模拟Ubuntu 18.04 + Qt5的线上环境，用于项目开发和测试。

## 🚀 快速开始

### 方法1: 使用快速启动脚本（推荐）

```bash
# 给脚本执行权限
chmod +x docker-quick.sh

# 一键启动（自动构建镜像并启动容器）
./docker-quick.sh
```

### 方法2: 使用完整管理脚本

```bash
# 给脚本执行权限
chmod +x docker-dev.sh

# 构建镜像
./docker-dev.sh build

# 启动容器
./docker-dev.sh start

# 查看帮助
./docker-dev.sh help
```

### 方法3: 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 进入容器
docker-compose exec tcp-qt5-dev /bin/bash

# 停止服务
docker-compose down
```

### 方法4: 手动Docker命令

```bash
# 构建镜像
docker build -t tcp-qt5-ubuntu18 .

# 启动容器（Linux/macOS with X11）
docker run -it --rm \
    --name tcp-qt5-dev \
    -v "$(pwd):/workspace" \
    -v /tmp/.X11-unix:/tmp/.X11-unix:rw \
    -e DISPLAY=$DISPLAY \
    -p 8888:8888 \
    --privileged \
    tcp-qt5-ubuntu18

# 启动容器（Windows或无GUI）
docker run -it --rm \
    --name tcp-qt5-dev \
    -v "$(pwd):/workspace" \
    -p 8888:8888 \
    tcp-qt5-ubuntu18
```

## 📁 文件说明

- `Dockerfile` - Docker镜像定义文件
- `docker-dev.sh` - 完整的Docker管理脚本
- `docker-quick.sh` - 快速启动脚本
- `docker-compose.yml` - Docker Compose配置
- `install_ubuntu18_deps.sh` - Ubuntu 18.04依赖安装脚本

## 🛠️ 环境信息

- **操作系统**: Ubuntu 18.04 LTS
- **Qt版本**: Qt5 (qt5-default)
- **CMake版本**: 3.10+
- **编译器**: GCC 7.5+
- **Boost版本**: 1.65+
- **其他库**: RapidJSON, hiredis, X11库

## 💻 容器内常用命令

```bash
# 查看环境信息
cat /etc/os-release
qmake --version
cmake --version

# 构建项目
mkdir -p build && cd build
cmake ..
make -j$(nproc)

# 运行程序
./tcp_qt5

# 查看项目结构
tree -L 2

# 测试网络连接
ping 127.0.0.1
telnet 127.0.0.1 8888
```

## 🖥️ GUI程序支持

### Linux/macOS
容器自动检测X11环境，支持运行Qt GUI程序。

### Windows
需要安装X11服务器（如VcXsrv、Xming）：

1. 安装VcXsrv
2. 启动XLaunch，选择"Disable access control"
3. 设置DISPLAY环境变量：
   ```bash
   export DISPLAY=host.docker.internal:0.0
   ```

## 🔧 管理脚本命令

`docker-dev.sh` 支持以下命令：

- `build` - 构建Docker镜像
- `start` - 启动开发容器
- `stop` - 停止开发容器
- `restart` - 重启开发容器
- `shell` - 进入容器shell
- `logs` - 查看容器日志
- `clean` - 清理容器和镜像
- `status` - 查看容器状态
- `help` - 显示帮助信息

## 📝 注意事项

1. **权限问题**: 容器内创建的文件可能有权限问题，可以在容器内使用 `chown` 修改
2. **端口冲突**: 默认映射8888-8890端口，如有冲突请修改配置
3. **X11转发**: GUI程序需要X11支持，Windows用户需要额外配置
4. **资源占用**: 容器会占用一定的系统资源，不用时建议停止

## 🐛 故障排除

### 构建失败
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
./docker-dev.sh build
```

### GUI程序无法显示
```bash
# 检查X11转发
echo $DISPLAY
ls -la /tmp/.X11-unix

# 允许X11连接（Linux）
xhost +local:docker
```

### 容器无法启动
```bash
# 查看详细错误
docker logs tcp-qt5-dev

# 检查端口占用
netstat -tlnp | grep 8888
```

## 🔄 更新环境

如需更新开发环境：

```bash
# 清理旧环境
./docker-dev.sh clean

# 重新构建
./docker-dev.sh build
```

## 📞 技术支持

如遇到问题，请检查：
1. Docker版本是否支持
2. 系统资源是否充足
3. 网络连接是否正常
4. 权限设置是否正确
