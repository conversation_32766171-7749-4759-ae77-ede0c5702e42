#!/bin/bash

# TCP Qt5 项目 Docker 开发环境启动脚本
# 用于快速启动Ubuntu18+Qt5开发环境

set -e

# 配置变量
IMAGE_NAME="tcp-qt5-ubuntu18"
CONTAINER_NAME="tcp-qt5-dev"
PROJECT_DIR="$(pwd)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "TCP Qt6 Docker 开发环境管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  build     构建Docker镜像"
    echo "  start     启动开发容器"
    echo "  stop      停止开发容器"
    echo "  restart   重启开发容器"
    echo "  shell     进入容器shell"
    echo "  logs      查看容器日志"
    echo "  clean     清理容器和镜像"
    echo "  status    查看容器状态"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build    # 构建镜像"
    echo "  $0 start    # 启动容器并进入shell"
    echo "  $0 shell    # 进入已运行的容器"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
}

# 构建Docker镜像
build_image() {
    print_info "开始构建Docker镜像: $IMAGE_NAME"
    
    if [ ! -f "Dockerfile" ]; then
        print_error "Dockerfile不存在，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    docker build -t $IMAGE_NAME . || {
        print_error "Docker镜像构建失败"
        exit 1
    }
    
    print_success "Docker镜像构建完成: $IMAGE_NAME"
}

# 启动容器
start_container() {
    print_info "启动开发容器: $CONTAINER_NAME"
    
    # 检查容器是否已存在
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        print_warning "容器 $CONTAINER_NAME 已存在"
        
        # 检查容器是否正在运行
        if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            print_info "容器已在运行，直接进入shell"
            docker exec -it $CONTAINER_NAME /bin/bash
            return
        else
            print_info "启动已存在的容器"
            docker start $CONTAINER_NAME
            docker exec -it $CONTAINER_NAME /bin/bash
            return
        fi
    fi
    
    # 检查镜像是否存在
    if ! docker images --format "table {{.Repository}}" | grep -q "^$IMAGE_NAME$"; then
        print_warning "镜像 $IMAGE_NAME 不存在，开始构建..."
        build_image
    fi
    
    # 启动新容器
    print_info "创建并启动新容器"
    
    # 检测X11支持
    X11_ARGS=""
    if [ -n "$DISPLAY" ] && [ -S "/tmp/.X11-unix" ]; then
        print_info "检测到X11环境，启用GUI支持"
        X11_ARGS="-e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix:rw"
    else
        print_warning "未检测到X11环境，GUI程序可能无法运行"
    fi
    
    docker run -it \
        --name $CONTAINER_NAME \
        --hostname tcp-qt5-dev \
        -v "$PROJECT_DIR:/workspace" \
        -p 8888:8888 \
        -p 8889:8889 \
        -p 8890:8890 \
        $X11_ARGS \
        --privileged \
        $IMAGE_NAME
}

# 停止容器
stop_container() {
    print_info "停止容器: $CONTAINER_NAME"
    
    if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        docker stop $CONTAINER_NAME
        print_success "容器已停止"
    else
        print_warning "容器未运行"
    fi
}

# 重启容器
restart_container() {
    stop_container
    sleep 2
    start_container
}

# 进入容器shell
enter_shell() {
    if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        print_info "进入容器shell"
        docker exec -it $CONTAINER_NAME /bin/bash
    else
        print_warning "容器未运行，启动容器..."
        start_container
    fi
}

# 查看容器日志
show_logs() {
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        print_info "显示容器日志"
        docker logs -f $CONTAINER_NAME
    else
        print_warning "容器不存在"
    fi
}

# 清理容器和镜像
clean_all() {
    print_warning "这将删除容器和镜像，确定继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        # 停止并删除容器
        if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            print_info "删除容器: $CONTAINER_NAME"
            docker rm -f $CONTAINER_NAME
        fi
        
        # 删除镜像
        if docker images --format "table {{.Repository}}" | grep -q "^$IMAGE_NAME$"; then
            print_info "删除镜像: $IMAGE_NAME"
            docker rmi $IMAGE_NAME
        fi
        
        print_success "清理完成"
    else
        print_info "取消清理操作"
    fi
}

# 查看容器状态
show_status() {
    print_info "容器状态信息:"
    echo ""
    
    # 检查镜像
    if docker images --format "table {{.Repository}}" | grep -q "^$IMAGE_NAME$"; then
        echo "✓ 镜像存在: $IMAGE_NAME"
        docker images | grep $IMAGE_NAME
    else
        echo "✗ 镜像不存在: $IMAGE_NAME"
    fi
    
    echo ""
    
    # 检查容器
    if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        echo "✓ 容器存在: $CONTAINER_NAME"
        docker ps -a | grep $CONTAINER_NAME
        
        if docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            echo "✓ 容器正在运行"
        else
            echo "✗ 容器已停止"
        fi
    else
        echo "✗ 容器不存在: $CONTAINER_NAME"
    fi
}

# 主函数
main() {
    # 检查Docker环境
    check_docker
    
    # 解析命令行参数
    case "${1:-start}" in
        build)
            build_image
            ;;
        start)
            start_container
            ;;
        stop)
            stop_container
            ;;
        restart)
            restart_container
            ;;
        shell)
            enter_shell
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_all
            ;;
        status)
            show_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
