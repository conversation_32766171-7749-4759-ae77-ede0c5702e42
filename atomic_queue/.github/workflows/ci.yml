name: Makefile Continuous Integrations

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  unit-test:
    strategy:
      matrix:
        toolset: [gcc, clang]
        os: [ubuntu-22.04, ubuntu-24.04]

    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4

    - name: Install Boost.Test
      run: sudo apt-get --quiet --yes install libboost-test-dev

    - name: Environment variables
      run: make -r TOOLSET=${{ matrix.toolset }} env

    - name: Toolset versions
      run: make -r TOOLSET=${{ matrix.toolset }} versions

    - name: Build and run unit tests
      run: make -rj2 TOOLSET=${{ matrix.toolset }} example run_tests

    - name: Build and run unit tests with thread sanitizer
      run: make -rj2 TOOLSET=${{ matrix.toolset }} BUILD=sanitize run_tests
