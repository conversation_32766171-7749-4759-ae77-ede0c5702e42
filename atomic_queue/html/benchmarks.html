<!DOCTYPE html>
<!-- Copyright (c) 2019 Maxim <PERSON>. MIT License. See the full licence in file LICENSE. -->
<html>
  <head>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-141287509-1"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'UA-141287509-1');
    </script>

    <link href="https://fonts.googleapis.com/css?family=Roboto+Slab:400,700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="benchmarks.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/highcharts-more.js"></script>
    <script src="https://code.highcharts.com/modules/pattern-fill.js"></script>
    <script src="theme.js"></script>
    <script src="benchmarks.js"></script>
    <meta charset="utf-8">
    <title>Scalability and Latency Benchmarks</title>
  </head>
  <body>
    <h1 class="view-toggle">Scalability Benchmark</h1>
    <div>
      <p>N producer threads push a 4-byte integer into one same queue, N consumer threads pop the integers from the queue. All producers posts 1,000,000 messages in total. Total time to send and receive all the messages is measured. The benchmark is run for from 1 producer and 1 consumer up to (total-number-of-cpus / 2) producers/consumers to measure the scalabilty of different queues. The minimum, maximum, mean and standard deviation of at least 33 runs are reported in the tooltip.</p>
      <h3 class="view-toggle">Scalability on Intel i9-9900KS</h3><div class="chart" id="scalability-9900KS-5GHz"></div>
      <h3 class="view-toggle">Scalability on AMD Ryzen 7 5825U</h3><div class="chart" id="scalability-ryzen-5825u"></div>
      <h3 class="view-toggle">Scalability on Intel Xeon Gold 6132</h3><div class="chart" id="scalability-xeon-gold-6132"></div>
      <h3 class="view-toggle">Scalability on AMD Ryzen 9 5950X</h3><div class="chart" id="scalability-ryzen-5950x"></div>
    </div>

    <h1 class="view-toggle">Latency Benchmark</h1>
    <div>
      <p>One thread posts a 4-byte integer to another thread through one queue and waits for a reply from another queue (2 queues in total). The benchmark measures the total time of 100,000 ping-pongs, best of 10 runs. Contention is minimal here (1-producer-1-consumer, 1 element in the queue) to be able to achieve and measure the lowest latency. Reports the average round-trip time, i.e. the time it takes to post a message to another thread and receive a reply. The minimum, maximum, mean and standard deviation of at least 33 runs are reported in the tooltip.</p>
      <h3 class="view-toggle">Latency on Intel i9-9900KS</h3><div class="chart" id="latency-9900KS-5GHz"></div>
      <h3 class="view-toggle">Latency on AMD Ryzen 7 5825U</h3><div class="chart" id="latency-ryzen-5825u"></div>
      <h3 class="view-toggle">Latency on Intel Xeon Gold 6132</h3><div class="chart" id="latency-xeon-gold-6132"></div>
      <h3 class="view-toggle">Latency on AMD Ryzen 9 5950X</h3><div class="chart" id="latency-ryzen-5950x"></div>
    </div>

    <h1 class="view-toggle">Systems details</h1>
    <div>
        <h3 class="view-toggle">Intel i9-9900KS system</h3>
        <ul>
          <li>OS: Kubuntu-18.04.4 LTS
          <li>Compiler: gcc-8.4.0
          <li>atomic_queue version: commit 7e138d21fcd4bad95e030d8d6c8b77d5a4538baa
          <li>Boost version: 1.65.1
          <li>TBB version: 2019_U7, commit 4233fef583b4f8cbf9f781311717600feaaa0694
          <li>moodycamel concurrentqueue version: commit dea078cf5b6e742cd67a0d725e36f872feca4de4
          <li>moodycamel readerwriterqueue version: commit 2ae710de996a1d02bbc7696b2cdff2c6078e76f8
          <li>xenium library version: commit f6416d30043a7d025405038d5ddd4794aaaab4a3
        </ul>
        <h3 class="view-toggle">AMD Ryzen 7 5825U system</h3>
        <ul>
          <li>OS: Kubuntu 22.04 LTS
          <li>Compiler: gcc-11.2.0
          <li>atomic_queue version: commit 7d75e9ed0359650224b29cdf6728c5fe0a19fffb
          <li>Boost version: 1.74.0
          <li>TBB version: 2021.5.0
          <li>moodycamel concurrentqueue version: commit dea078cf5b6e742cd67a0d725e36f872feca4de4
          <li>moodycamel readerwriterqueue version: commit 2ae710de996a1d02bbc7696b2cdff2c6078e76f8
          <li>xenium library version: commit f6416d30043a7d025405038d5ddd4794aaaab4a3
        </ul>
        <h3 class="view-toggle">Intel Xeon Gold 6132 system</h3>
        <ul>
          <li>OS: Red Hat Enterprise Linux Server release 6.10 (Santiago)
          <li>Compiler: gcc-8.4.0
          <li>atomic_queue version: commit 7e138d21fcd4bad95e030d8d6c8b77d5a4538baa
          <li>Boost version: 1.65.1
          <li>TBB version: 2019_U7, commit 4233fef583b4f8cbf9f781311717600feaaa0694
          <li>moodycamel concurrentqueue version: commit dea078cf5b6e742cd67a0d725e36f872feca4de4
          <li>moodycamel readerwriterqueue version: commit 2ae710de996a1d02bbc7696b2cdff2c6078e76f8
          <li>xenium library version: commit f6416d30043a7d025405038d5ddd4794aaaab4a3
        </ul>
        <h3 class="view-toggle">AMD Ryzen 9 5950X system</h3>
        <ul>
          <li>OS: KDE Neon based on Ubuntu 20.04 and XanMod Kernel 5.10 LTS.
          <li>Compiler: gcc-9.3.0
          <li>atomic_queue version: commit e02078c14cab70f0df594ea3406f1240297e11d7
          <li>Boost version: 1.71.0
          <li>TBB version: 2019_U7, commit 4233fef583b4f8cbf9f781311717600feaaa0694
          <li>moodycamel concurrentqueue version: commit dea078cf5b6e742cd67a0d725e36f872feca4de4
          <li>moodycamel readerwriterqueue version: commit 2ae710de996a1d02bbc7696b2cdff2c6078e76f8
          <li>xenium library version: commit f6416d30043a7d025405038d5ddd4794aaaab4a3
        </ul>
    </div>

    <h1 class="view-toggle homepage">Homepage</h1>
    <div>
      <p class="homepage"><a href="https://github.com/max0x7ba/atomic_queue">github.com/max0x7ba/atomic_queue</a></p>
      <p class="copyright">Copyright (c) 2019 Maxim Egorushkin. MIT License. See the full licence in file LICENSE.</p>
    </div>
  </body>
</html>
