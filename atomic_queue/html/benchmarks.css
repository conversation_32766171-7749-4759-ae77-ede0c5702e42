body {
    visibility: hidden;
    background-color: black;
    color: white;
    font-family: 'Roboto Slab', sans-serif;
    overflow-y: scroll;
}

h1, h2, h3 {
    margin-left: 20px;
}

p, li {
    color: #A0A0A0;
    margin-left: 20px;
}

ul {
    margin-top: .5em;
    margin-bottom: 1em;
}

div.chart {
    height: 500px;
    margin-bottom: 1em;
}

p.copyright {
    color: #A0A0A0;
    font-size: 0.8em;
}

h1.homepage {
    margin-bottom: 0em;
}

p.homepage {
    color: #A0A0A0;
    margin-top: 0em;
    font-weight: bold;
}

.view-toggle {
    cursor: pointer;
    margin-bottom: 0em;
    user-select: none;
}

h3.view-toggle {
    margin-left: 24px;
    margin-top: 0em;
}

svg.arrow-down-circle {
    fill: currentColor;
    width: 1em;
    height: 1em;
    color: #A0A0A0;
    vertical-align: -.1em;
}

span.tooltip_scalability_title {
    font-weight: bold;
    font-size: 1.2em;
}

table.tooltip_scalability {
    text-align: right;
}
