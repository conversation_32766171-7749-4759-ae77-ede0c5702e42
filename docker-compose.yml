version: '3.8'

services:
  tcp-qt5-dev:
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
    image: tcp-qt5-ubuntu18
    container_name: tcp-qt5-dev
    hostname: tcp-qt5-dev
    
    # 挂载项目目录
    volumes:
      - .:/workspace
      # X11支持（Linux/macOS）
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    
    # 端口映射
    ports:
      - "8888:8888"
      - "8889:8889" 
      - "8890:8890"
    
    # 环境变量
    environment:
      - DISPLAY=${DISPLAY}
      - QT_SELECT=qt5
      - TZ=Asia/Shanghai
    
    # 特权模式（用于调试）
    privileged: true
    
    # 保持容器运行
    tty: true
    stdin_open: true
    
    # 工作目录
    working_dir: /workspace
    
    # 默认命令
    command: /bin/bash
    
    # 网络模式
    network_mode: bridge
    
    # 重启策略
    restart: unless-stopped
