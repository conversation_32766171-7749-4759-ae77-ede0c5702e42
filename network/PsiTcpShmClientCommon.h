#pragma once
#include "PsiTcpShmConn.h"

#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN

template<class Derived>
class TcpShmClient
{
public:
    using Connection = TcpShmConnection<ClientConf>;
    using LoginMsg = LoginMsgTpl;
    using LoginRspMsg = LoginRspMsgTpl;

protected:
    /**
     * 构造函数
     * @param ptcp_dir PTCP 目录
     * @param client_name 客户端名称
     * @param client_conf PTCP 目录
     */
    TcpShmClient(const std::string& ptcp_dir,const std::string& client_name,const ClientConf& client_conf);

    /**
     * 析构函数实现
     */
    virtual ~TcpShmClient();

    /**
     * 连接并登录服务器
     * @param use_shm 是否使用共享内存
     * @param server_ipv4 服务器 IPv4 地址
     * @param server_port 服务器端口
     * @param login_user_data 登录用户数据
     * @return 连接成功返回 true，否则返回 false
     */
    bool Connect(bool use_shm,
                 const char* server_ipv4,
                 uint16_t server_port,
                 const CommonConf::LoginUserData& login_user_data);

    /**
     * 轮询 TCP 连接
     * @param now 当前时间
     */
    void PollTcp(int64_t now);

    /**
     * 轮询共享内存
     */
    void PollShm();

    /**
     * 停止连接并关闭文件
     */
    void Stop();

    /**
     * 获取连接引用
     * @return 连接引用
     */
    Connection& GetConnection();

private:
    const ClientConf& conf_;

    char client_name_[CommonConf::NameSize]{};//定义客户端名称，字符串类型，长度是CommonConf::NameSize
    using ServerName = char[CommonConf::NameSize];
    char* server_name_ = nullptr;
    std::string ptcp_dir_;
    Connection conn_;
};
NS_PSI_TCP_SHM_END

#include "./PsiTcpShmClientCommon.tpp"
