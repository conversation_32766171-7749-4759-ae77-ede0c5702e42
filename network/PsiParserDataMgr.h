/*!
 * \file PsiTraderDataMgr.cpp
 * \project	PsiTraderMagicWeapon
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 存储交易相关数据的管理类
 */
#pragma once
#include "PsiDataDef.hpp"
#include "PsiContractInfo.hpp"
#include "PsiVariant.hpp"
#include <boost/filesystem.hpp>
#include <boost/format.hpp>
#include "PsiMarcos.h"
#include "PsiBaseDataMgr.h"
#include "StrUtil.hpp"
#include "StdUtils.hpp"
#include "TORATstpLev2MdApi.h"
#include "TORATstpXMdApi.h"
#include "ThostFtdcUserApiStruct.h"
//using namespace TORALEV2API;
class PsiParserDataMgr {
public:
    PsiParserDataMgr();
    ~PsiParserDataMgr();

    /**
     * 初始化
     * @param psiBaseDataMgr
     * @return
     */
    bool init(PsiBaseDataMgr* psiBaseDataMgr, const char* parserId, const char* parserModel, const char* market, std::vector<std::string> stdCodes,
              std::string subscribeType, bool flag= false);

    /**
     * 设置当前行情订阅逐笔数据
     * @param flag
     */
    void setSubscribeTranOrOrder(bool flag= false);

    /**
     * 运行
     * @return
     */
    bool run();
public:
    std::string m_parserId; // 行情模块ID
    std::string m_parserModel; // 行情模块
    std::string m_subscribeType; // 订阅数据类型
    std::string m_marketId;
    bool m_isSubscribeTranOrOrder = false;
    PsiBaseDataMgr* mp_baseDataMgr;
    TORALEV2API::CTORATstpLev2OrderDetailField m_orderDetailFields[LENGTHS];
    PSILev2OrderDetailSubStruct m_orderDetailSubs[LENGTHS];
    int m_orderDetailIndex = 0;
    int m_orderHandleIndex = 0;
    TORALEV2API::CTORATstpLev2TransactionField m_transactionFields[LENGTHS];
    PSILev2TransactionSubStruct m_transDetailSubs[LENGTHS];
    int m_transactionIndex = 0;
    int m_transactionHandleIndex = 0;

    PSIL2SealOpenBoardStruct m_openBoard[TRANS_TOTAL_LENGTH]; // 存储开板数据
    int m_openBoardIndex = 0; // 存储开板数据索引

    TORALEV2API::CTORATstpLev2MarketDataField m_lev2MarketDataField[L2_LENGTHS]; // 存储快照数据
    PSILev2MaketDataSubStruct m_lev2MarketDataSubs[L2_LENGTHS]; // 存储快照数据
    int m_lev2MarketDataIndex = 0; // 存储快照数据索引
    int m_lev2MarketDataHandleIndex = 0; // 存储快照数据索引

    std::vector<std::string> m_stdCodes; // 标的代码列表

    //L1行情
    TORALEV1API::CTORATstpMarketDataField m_lev1MarketDataField[L1_LENGTHS]; // 存储快照数据
    PSILev2MaketDataSubStruct m_lev1MarketDataSubs[L1_LENGTHS]; // 存储快照数据
    int m_lev1MarketDataIndex = 0; // 存储快照数据索引
    int m_lev1MarketDataHandleIndex = 0; // 存储快照数据索引

    // CTP L1
    CThostFtdcDepthMarketDataField m_ctpL1MarketDataField[L1_LENGTHS]; // 存储快照数据
    PSILev2MaketDataSubStruct m_ctpL1MarketDataSubs[L1_LENGTHS]; // 存储快照数据
    int m_ctpL1MarketDataIndex = 0; // 存储快照数据索引
    int m_ctpL1MarketDataHandleIndex = 0; // 存储快照数据索引
};

