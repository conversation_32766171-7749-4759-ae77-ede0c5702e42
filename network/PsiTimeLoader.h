/*!
 * \file PsiTimeLoader.cpp
 * \project	PsiTraderMagicWeapon
 *
* \author l<PERSON><PERSON>
* \date 2024/03/05
 *
 * \brief 时间加载器
 */
#pragma once
#include "PsiMarcos.h"
#include "StdUtils.hpp"
#include <atomic>
#include <chrono>

class PsiTimeLoader {
public:
    PsiTimeLoader();
    ~PsiTimeLoader();

    /**
     * @brief 初始化
     * @return
     */
    bool init(const int cpuCore=0);

    /**
     * @brief 启动
     * @return
     */
    bool run();

    /**
     * 获取系统初始时间
     * @return
     */
    inline int64_t getSysTime() const{
        return m_sysTime;
    }

    /**
     * 获取当前时间click
     * @return
     */
    inline int64_t getCurTime() const{
        return m_curTime.load();
    }

    /**
     * 获取当前时间click
     * @return
     */
    inline uint64_t getCurNowTime() const{
        return m_curNowTime.load();
    }

private:
    unsigned long long m_sysTime; // 系统时间

    std::chrono::time_point<std::chrono::high_resolution_clock> m_initTime; // 初始化时间

    std::chrono::time_point<std::chrono::high_resolution_clock> m_lastTime; // 上次时间

    std::atomic<int64_t> m_curTime; // 当前时间
    std::atomic<uint64_t> m_curNowTime;

    int m_cpuCore = 0; // 计算线程绑定的CPU核心

    StdThreadPtr m_threadWorker; // 线程对象
};



