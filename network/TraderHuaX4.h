/*!
 * \file TraderHuaX.h
 * \project	PsiTraderMagicWeapon
 *
 * \author liji<PERSON>
 * \date 2024/02/05
 * 
 * \brief 
 */
#pragma once

#include <stdint.h>
#include <string>
#include <boost/asio.hpp>
// 函数: Boost.Asio 版本兼容性处理
// 作用: Ubuntu 18.04的Boost版本较老，可能没有executor_work_guard.hpp
#if BOOST_VERSION >= 106600
#include <boost/asio/executor_work_guard.hpp>
#else
// 使用旧版本的 io_service::work 替代
#include <boost/asio/io_service.hpp>
#endif

#include "PsiCollection.hpp"

#include "StdUtils.hpp"
#include "PsiKVCache.hpp"

#include "PsiContractInfo.hpp"
#include "PsiVariant.hpp"
#include "StrUtil.hpp"
#include "TimeUtils.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiTraderDataMgr.h"
#include "PsiRedisDataMgr.h"
#include "PsiDataMgr.h"
#include <boost/format.hpp>

#include <boost/filesystem.hpp>

#include "TORATstpTraderApi.h"



typedef TORASTOCKAPI::CTORATstpTraderSpi HuaXTraderSpi;
typedef TORASTOCKAPI::CTORATstpTraderApi HuaXTraderApi;



class TraderHuaX4 : public HuaXTraderSpi
{
public:
    TraderHuaX4();
    ~TraderHuaX4();

public:
    //////////////////////////////////////////////////////////////////////////
    //HuaX::API::TraderSpi �ӿ�
    virtual void OnFrontConnected() override;
    ///���ͻ����뽻�׺�̨ͨ�����ӶϿ�ʱ���÷��������á���������������API���Զ��������ӣ��ͻ��˿ɲ���������
    ///@param nReason ����ԭ��
    ///        -3 �����ѶϿ�
    ///        -4 �����ʧ��
    ///        -5 ����дʧ��
    ///        -6 ����������
    ///        -7 ����Ŵ���
    ///        -8 �������������
    ///        -9 ����ı���
    ///		  -15 �����ʧ��
    ///		  -16 ����дʧ��
    virtual void OnFrontDisconnected(int nReason) override;

    ///����Ӧ��
    virtual void OnRspError(TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ȡ������ϢӦ��
    virtual void OnRspGetConnectionInfo(TORASTOCKAPI::CTORATstpConnectionInfoField *pConnectionInfoField, TORASTOCKAPI::CTORATstpRspInfoField *pRspInfo, int nRequestID) override;

    ///��¼��Ӧ
    virtual void OnRspUserLogin(TORASTOCKAPI::CTORATstpRspUserLoginField* pRspUserLoginField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///�ǳ���Ӧ
    virtual void OnRspUserLogout(TORASTOCKAPI::CTORATstpUserLogoutField *pUserLogoutField, TORASTOCKAPI::CTORATstpRspInfoField *pRspInfoField, int nRequestID) override;

    ///����¼����Ӧ
    virtual void OnRspOrderInsert(TORASTOCKAPI::CTORATstpInputOrderField* pInputOrderField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///�����ر�
    virtual void OnRtnOrder(TORASTOCKAPI::CTORATstpOrderField* pOrderField) override;

    ///��������ر�
    virtual void OnErrRtnOrderInsert(TORASTOCKAPI::CTORATstpInputOrderField* pInputOrderField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///�ɽ��ر�
    virtual void OnRtnTrade(TORASTOCKAPI::CTORATstpTradeField* pTradeField) override;

    ///������Ӧ
    virtual void OnRspOrderAction(TORASTOCKAPI::CTORATstpInputOrderActionField *pInputOrderActionField, TORASTOCKAPI::CTORATstpRspInfoField *pRspInfoField, int nRequestID) override;

    ///��������ر�
    virtual void OnErrRtnOrderAction(TORASTOCKAPI::CTORATstpInputOrderActionField* pInputOrderActionField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///��ѯ������Ӧ
    virtual void OnRspQryOrder(TORASTOCKAPI::CTORATstpOrderField* pOrderField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯ�ɽ���Ӧ
    virtual void OnRspQryTrade(TORASTOCKAPI::CTORATstpTradeField* pTradeField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯͶ���ֲ߳���Ӧ
    virtual void OnRspQryPosition(TORASTOCKAPI::CTORATstpPositionField* pPositionField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯ�ʽ��˻���Ӧ
    virtual void OnRspQryTradingAccount(TORASTOCKAPI::CTORATstpTradingAccountField* pTradingAccountField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯ�ɶ��˻���Ӧ
    virtual void OnRspQryShareholderAccount(TORASTOCKAPI::CTORATstpShareholderAccountField* pShareholderAccountField, TORASTOCKAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

public:
    //////////////////////////////////////////////////////////////////////////
    //ITraderApi �ӿ�
    bool init();

    void release();

    void registerSpi(PsiBaseDataMgr* pBdMgr, PsiDataMgr *dataMgr, PsiTraderDataMgr* pTdMgr, PsiRedisDataMgr* pRdMgr);

    void connect();

    void disconnect();

    bool isConnected();

    bool makeEntrustID(char* buffer, int length);

    int login();

    int logout();

    /*
     *	�µ��ӿ�(����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderBuy(int orderRef, const char* code, const char* exch, const char* userTag, int64_t volume, double price);

    /*
     *	�µ��ӿ�(����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderSell(int orderRef, const char* code, const char* exch, const char* userTag, int64_t volume, double price);

    /*
     *	�µ��ӿ�(����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderCancel(int orderRef, const char* orderSysId, const char* exch);

    int orderAction();

    int queryAccount();

    int queryPositions();

    int queryCodePositions(const char* code);

    int queryOrders();

    int queryTrades();

    void function1();

private:
    void		reconnect();
    void		doLogin();
    inline bool	extractEntrustID(const char* entrustid, int &orderRef);
    inline void	genEntrustID(char* buffer, uint32_t orderRef);

private:
    HuaXTraderApi*	m_api;

    typedef PsiHashMap<std::string> PositionMap;
    PsiArray*				m_positions;
    PsiArray*				m_trades;
    PsiArray*				m_orders;
    PsiArray*				m_accounts;

    uint32_t		m_tradingday;

#if BOOST_VERSION >= 106600
    boost::asio::io_context m_asyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_work{m_asyncio.get_executor()};
#else
    boost::asio::io_service m_asyncio;
    std::unique_ptr<boost::asio::io_service::work> m_work;
#endif
    StdThreadPtr				m_thrdWorker;

#if BOOST_VERSION >= 106600
    boost::asio::io_context m_traderAsyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_traderWork{m_traderAsyncio.get_executor()};
#else
    boost::asio::io_service m_traderAsyncio;
    std::unique_ptr<boost::asio::io_service::work> m_traderWork;
#endif
    StdThreadPtr				m_traderWorker;

    StdThreadPtr				m_queryWorker;

    //ί�е���ǻ�����
    WtKVCache		m_eidCache;
    //������ǻ�����
    WtKVCache		m_oidCache;

    PsiBaseDataMgr* mp_bdMgr; // �������ݹ�����
    PsiDataMgr*     mp_dataMgr; // ���ݹ�����
    PsiTraderDataMgr* mp_tdMgr; // �������ݹ�����
    PsiRedisDataMgr* mp_rdMgr; // Redis���ݹ�����
    bool m_bCheckTrade;    // �Ƿ��齻��ģ��
};

