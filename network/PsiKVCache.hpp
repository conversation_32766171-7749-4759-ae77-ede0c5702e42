#pragma once
#include "PsiSpinMutex.hpp"
#include "PsiFasterDefs.h"

#include <boost/interprocess/file_mapping.hpp>
#include <boost/interprocess/mapped_region.hpp>
#include <boost/filesystem.hpp>
#include <fstream>
#include <functional>
#include <memory>
#include <cstring>

#define SIZE_STEP 200
#define CACHE_FLAG "&^%$#@!\0"
#define FLAG_SIZE 8

namespace bip = boost::interprocess;
namespace fs = boost::filesystem;

#pragma warning(disable:4200)

class BoostMappingFile;

typedef std::shared_ptr<BoostMappingFile> BoostMFPtr;

typedef std::function<void(const char*)> CacheLogger;

class BoostMappingFile
{
public:
    BoostMappingFile() : _addr(nullptr), _size(0) {}

    bool map(const char* filename)
    {
        try {
            if (!fs::exists(filename))
                return false;

            _filename = filename;
            _mapping = bip::file_mapping(filename, bip::read_write);
            _region = bip::mapped_region(_mapping, bip::read_write);
            _addr = _region.get_address();
            _size = _region.get_size();
            return true;
        }
        catch (...) {
            return false;
        }
    }

    void* addr() const { return _addr; }
    uint64_t size() const { return _size; }
    const std::string& filename() const { return _filename; }

private:
    std::string _filename;
    bip::file_mapping _mapping;
    bip::mapped_region _region;
    void* _addr;
    uint64_t _size;
};

class WtKVCache
{
public:
    WtKVCache() {}
    WtKVCache(const WtKVCache&) = delete;
    WtKVCache& operator=(const WtKVCache&) = delete;

private:
    typedef struct _CacheItem
    {
        char	_key[64] = { 0 };
        char	_val[64] = { 0 };
    } CacheItem;

    typedef struct CacheBlock
    {
        char		_blk_flag[FLAG_SIZE];
        uint32_t	_size;
        uint32_t	_capacity;
        uint32_t	_date;
        CacheItem	_items[0];
    } CacheBlock;

    typedef struct _CacheBlockPair
    {
        CacheBlock*		_block;
        BoostMFPtr		_file;

        _CacheBlockPair()
        {
            _block = NULL;
            _file = NULL;
        }
    } CacheBlockPair;

    CacheBlockPair	_cache;
    PsiSpinMutex		_lock;
    psi_hashmap<std::string, uint32_t> _indice;

private:
    bool	resize(uint32_t newCap, CacheLogger logger = nullptr)
    {
        if (_cache._file == NULL)
            return false;

        CacheBlock* cBlock = _cache._block;
        if (cBlock->_capacity >= newCap)
            return true;

        std::string filename = _cache._file->filename();
        uint64_t uOldSize = sizeof(CacheBlock) + sizeof(CacheItem)*cBlock->_capacity;
        uint64_t uNewSize = sizeof(CacheBlock) + sizeof(CacheItem)*newCap;

        _cache._file.reset();

        try {
            fs::resize_file(filename, uNewSize);
        }
        catch (std::exception& e) {
            if (logger) logger(e.what());
            return false;
        }

        BoostMappingFile* pNewMf = new BoostMappingFile();
        if (!pNewMf->map(filename.c_str())) {
            delete pNewMf;
            if (logger) logger("Mapping cache file failed");
            return false;
        }

        _cache._file.reset(pNewMf);
        _cache._block = (CacheBlock*)_cache._file->addr();
        _cache._block->_capacity = newCap;
        return true;
    }

public:
    bool	init(const char* filename, uint32_t uDate, CacheLogger logger = nullptr)
    {
        bool isNew = false;
        if (!fs::exists(filename))
        {
            uint64_t uSize = sizeof(CacheBlock) + sizeof(CacheItem) * SIZE_STEP;
            std::ofstream ofs(filename, std::ios::binary);
            if (!ofs) {
                if (logger) logger("Create cache file failed");
                return false;
            }
            ofs.close();
            try {
                fs::resize_file(filename, uSize);
            }
            catch (std::exception& e) {
                if (logger) logger(e.what());
                return false;
            }
            isNew = true;
        }

        _cache._file.reset(new BoostMappingFile);
        if (!_cache._file->map(filename)) {
            _cache._file.reset();
            if (logger) logger("Mapping cache file failed");
            return false;
        }
        _cache._block = (CacheBlock*)_cache._file->addr();

        if (!isNew && _cache._block->_date != uDate) {
            _cache._block->_size = 0;
            _cache._block->_date = uDate;
            memset(_cache._block->_items, 0, sizeof(CacheItem)*_cache._block->_capacity);
            if (logger) logger("Cache file reset due to a different date");
        }

        if (isNew) {
            _cache._block->_capacity = SIZE_STEP;
            _cache._block->_size = 0;
            _cache._block->_date = uDate;
            strcpy(_cache._block->_blk_flag, CACHE_FLAG);
        }
        else {
            uint64_t realSz = _cache._file->size();
            uint64_t expectSz = sizeof(CacheBlock) + sizeof(CacheItem)*_cache._block->_capacity;
            if (realSz != expectSz) {
                uint32_t realCap = (uint32_t)((realSz - sizeof(CacheBlock)) / sizeof(CacheItem));
                _cache._block->_capacity = realCap;
                if (_cache._block->_size > realCap)
                    _cache._block->_size = realCap;
            }
        }

        for (uint32_t i = 0; i < _cache._block->_size; i++)
            _indice[_cache._block->_items[i]._key] = i;

        return true;
    }

    inline void clear()
    {
        _lock.lock();
        if (_cache._block) {
            _indice.clear();
            memset(_cache._block->_items, 0, sizeof(CacheItem)*_cache._block->_capacity);
            _cache._block->_size = 0;
        }
        _lock.unlock();
    }

    inline const char*	get(const char* key) const
    {
        auto it = _indice.find(key);
        return it == _indice.end() ? "" : _cache._block->_items[it->second]._val;
    }

    void	put(const char* key, const char* val, std::size_t len = 0, CacheLogger logger = nullptr)
    {
        auto it = _indice.find(key);
        if (it != _indice.end()) {
            mw_strcpy(_cache._block->_items[it->second]._val, val, len);
        }
        else {
            _lock.lock();
            if (_cache._block->_size == _cache._block->_capacity &&
                !resize(_cache._block->_capacity * 2, logger))
            {
                _lock.unlock();
                return;
            }
            _indice[key] = _cache._block->_size;
            mw_strcpy(_cache._block->_items[_cache._block->_size]._key, key);
            mw_strcpy(_cache._block->_items[_cache._block->_size]._val, val, len);
            _cache._block->_size++;
            _lock.unlock();
        }
    }

    inline bool	has(const char* key) const
    {
        return _indice.find(key) != _indice.end();
    }

    inline uint32_t size() const
    {
        return _cache._block ? _cache._block->_size : 0;
    }

    inline uint32_t capacity() const
    {
        return _cache._block ? _cache._block->_capacity : 0;
    }
};
