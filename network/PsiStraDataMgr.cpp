/*!
 * \file PsiStraDataMgr.cpp
 * \project	PsiStraDataMgr
 *
* \author liji<PERSON>
* \date 2024/02/05
 *
 * \brief 存储交易相关数据的管理类
 */

#include "PsiStraDataMgr.h"

/**
 * 生成一个自增的StraId
 * @return
 */
inline uint32_t makeCtxId()
{
    static std::atomic<uint32_t> _auto_context_id{ 6000 };
    return _auto_context_id.fetch_add(1);
}

PsiStraDataMgr::PsiStraDataMgr()
        : m_straId(0)
        , mp_traderDataMgr(NULL)
        , m_bRunning(false)
        , m_maxNum(1)
        , m_maxVolume(100)
        , m_maxAmount(50000)
        , m_orderNum(0)
        , m_startTime(93000000)
        , m_endTime(145700000)
        , m_maxPorderVolume(0)
        , m_maxPorderAmount(-500000)
        , m_maxPriceProportion(8.0)
        , m_placeOrderTime(0)
        , m_isPlaceOrderTimeActive(false)
        , m_filterStockNum(0)
        , m_isFilterStockNumActive(false)
        , m_maxDrainageNum(0)
        , m_isMaxDrainageNumActive(false)
        , m_isMaxPorderAmountActive(false)
        , m_mainBuyAmount(0.0)
        , m_isMainBuyAmountActive(false)
        , m_boardOrderSellAmount(0.0)
        , m_isBoardOrderSellAmountActive(false)
{
    m_straId = makeCtxId();
}

PsiStraDataMgr::~PsiStraDataMgr() {

}

void PsiStraDataMgr::init(PsiTraderDataMgr* trader, PsiStraShareDataMgr* shareDataMgr) {
    if(NULL == mp_traderDataMgr){
        mp_traderDataMgr = trader;
    }
    if(NULL == mp_shareDataMgr){
        mp_shareDataMgr = shareDataMgr;
    }
}