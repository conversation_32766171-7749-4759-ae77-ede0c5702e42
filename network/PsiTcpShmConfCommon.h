#pragma once
#include <cstdint>
#include <ctime>
#include "PsiVariant.hpp"

#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN
    struct CommonConf {
        //这里不太好修改…涉及到申请固定大小内存
        static constexpr uint32_t NameSize = 16;
        static constexpr bool ToLittleEndian = true; // set to the endian of majority of the hosts
        static constexpr uint32_t ShmQueueSize = 1048576; // must be power of 2

        //  客户端/服务端共有配置
        uint32_t TcpRecvBufInitSize; // must be a multiple of 8
        uint32_t TcpRecvBufMaxSize; // must be a multiple of 8
        int64_t ConnectionTimeout;
        int64_t HeartBeatInverval;
        bool TcpNoDelay;

        using LoginUserData = char;
        using LoginRspUserData = char;
        using ConnectionUserData = char;
    };

    struct ClientConf : CommonConf {
        static constexpr uint32_t TcpQueueSize = **********;
        int Index = 0;
        int CpuCore = 0;
        std::string ClientName;
        std::string ServerAddr;
        uint16_t ServerPort;
        bool UseShm;
        std::string AccountId;
        void Load(const PsiVariant* cfgItem, int index=0) {
             ClientName = cfgItem->getCString("client_name");
             ServerAddr = cfgItem->getCString("server_addr");
             ServerPort = cfgItem->getUInt32("server_port");
             UseShm = cfgItem->getBoolean("use_shm");
             TcpRecvBufInitSize = cfgItem->getUInt32("tcp_recv_buf_init_size");
             TcpRecvBufMaxSize = cfgItem->getUInt32("tcp_recv_buf_max_size");
             TcpNoDelay = cfgItem->getBoolean("tcp_no_delay");
             ConnectionTimeout = cfgItem->getInt64("connection_timeout");
             HeartBeatInverval = cfgItem->getInt64("heart_beat_interval");
             AccountId = cfgItem->getCString("account_id");
             CpuCore = cfgItem->getInt32("cpu_core");
             Index = index;
        }
    };

    struct ServerConf : CommonConf {
        static constexpr uint32_t TcpQueueSize = 1048576; //8的倍数
        static constexpr uint32_t MaxShmConnsPerGrp = 4;
        static constexpr uint32_t MaxTcpConnsPerGrp = 4;
        int Index = 0;
        int CpuCore = 0;
        std::string ServerName;
        std::string ServerIp; // 服务器ip
        uint16_t ListenPort;
        uint32_t MaxNewConnections;
        uint32_t MaxShmGrps;
        uint32_t MaxTcpGrps;

        uint64_t NewConnectionTimeout;

        void Load(const PsiVariant* cfgItem, int index=0) {
            ServerName = cfgItem->getCString("server_name");
            ListenPort = cfgItem->getUInt32("listen_port");
            MaxNewConnections = cfgItem->getUInt32("max_new_connections");
            MaxShmGrps = cfgItem->getUInt32("max_shm_grps");
            MaxTcpGrps = cfgItem->getUInt32("max_tcp_grps");
            TcpRecvBufInitSize = cfgItem->getUInt32("tcp_recv_buf_init_size");
            TcpRecvBufMaxSize = cfgItem->getUInt32("tcp_recv_buf_max_size");
            TcpNoDelay = cfgItem->getBoolean("tcp_no_delay");
            NewConnectionTimeout = cfgItem->getUInt64("new_connection_timeout");
            ConnectionTimeout = cfgItem->getInt64("connection_timeout");
            HeartBeatInverval = cfgItem->getInt64("heart_beat_interval");
            CpuCore = cfgItem->getInt32("cpu_core");
            ServerIp = cfgItem->getCString("server_ip");
            Index = index;
        }
    };


    template<uint16_t MsgType>
    struct MsgTpl {
        static const uint16_t msg_type = MsgType;
    };

    // 跨平台高精度时间：Windows 使用 QPC，其他平台使用 std::chrono
    inline unsigned long long now() {
    #ifdef _WIN32
        LARGE_INTEGER freq, counter;
        QueryPerformanceFrequency(&freq);
        QueryPerformanceCounter(&counter);
        return (counter.QuadPart * 1000000000) / freq.QuadPart;
    #else
        using clock = std::chrono::high_resolution_clock;
        auto tp = clock::now().time_since_epoch();
        return static_cast<unsigned long long>(std::chrono::duration_cast<std::chrono::nanoseconds>(tp).count());
    #endif
    }

NS_PSI_TCP_SHM_END
