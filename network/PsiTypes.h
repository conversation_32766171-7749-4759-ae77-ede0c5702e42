#pragma once
#include "PsiMarcos.h"
#include <stdint.h>


typedef enum tagContractCategory
{
	CC_Stock,
	CC_Future,
	CC_FutOption,
	CC_Combination,
	CC_Spot,
	CC_EFP,
	CC_SpotOption,
	CC_ETFOption,

	CC_DC_Spot	= 20,
	CC_DC_Swap,
	CC_DC_Future,
	CC_DC_Margin,
	CC_DC_Option,

	CC_UserIndex = 90
} ContractCategory;

typedef enum stockContractInfo{
    SC_0000001 = 1,
    SC_0000002 = 2
}StockContractInfo;

/*
 *
 */
typedef enum tagOptionType
{
	OT_None = 0,
	OT_Call = '1',
	OT_Put	= '2'
} OptionType;

/*
 *
 */
typedef enum tagCoverMode
{
	CM_OpenCover,
	CM_CoverToday,
	CM_UNFINISHED,
	CM_None
} CoverMode;

/*
 *
 */
typedef enum tagTradingMode
{
	TM_Both,
	TM_Long,
	TM_LongT1,
	TM_None = 9
} TradingMode;

/*
*
*/
typedef enum tagPriceMode
{
	PM_Both,
	PM_Limit,
	PM_Market,
	PM_None	= 9
} PriceMode;

/*
 *
 *
 */
typedef enum tagKlineFieldType
{
	KFT_OPEN,
	KFT_HIGH,
	KFT_LOW,
	KFT_CLOSE,
	KFT_DATE,
	KFT_TIME,
	KFT_VOLUME,
	KFT_SVOLUME
} WTSKlineFieldType;

/*
 *
 */
typedef enum tagKlinePeriod
{
	KP_Tick,
	KP_Minute1,
    KP_Minute3,
	KP_Minute5,
    KP_Minute10,
    KP_Minute15,
    KP_Minute30,
    KP_Minute60,
	KP_DAY,
	KP_Week,
	KP_Month
} PSIKlinePeriod;



static const char* PERIOD_NAME[] = 
{
	"tick",
	"min1",
	"min5",
	"day",
	"week",
	"month"
};

/*
 *
 */
typedef enum tagLogLevel
{
	LL_ALL	= 100,
	LL_DEBUG,
	LL_INFO,
	LL_WARN,
	LL_ERROR,
	LL_FATAL,
	LL_NONE
} WTSLogLevel;

/*
 *
 */
typedef enum tagPriceType
{
	WPT_ANYPRICE	= 0,
	WPT_LIMITPRICE,
	WPT_BESTPRICE,
	WPT_LASTPRICE,

	//////////////////////////////////////////////////////////////////////////
	WPT_CTP_LASTPLUSONETICKS = 20,
	WPT_CTP_LASTPLUSTWOTICKS,
	WPT_CTP_LASTPLUSTHREETICKS,
	WPT_CTP_ASK1,
	WPT_CTP_ASK1PLUSONETICKS,
	WPT_CTP_ASK1PLUSTWOTICKS,
	WPT_CTP_ASK1PLUSTHREETICKS,
	WPT_CTP_BID1,
	WPT_CTP_BID1PLUSONETICKS,
	WPT_CTP_BID1PLUSTWOTICKS,
	WPT_CTP_BID1PLUSTHREETICKS,
	WPT_CTP_FIVELEVELPRICE,

	//////////////////////////////////////////////////////////////////////////
	WPT_DC_POSTONLY	= 100,
	WPT_DC_FOK,
	WPT_DC_IOC,
	WPT_DC_OPTLIMITIOC
} WTSPriceType;

/*
 *
 */
typedef enum tagTimeCondition
{
	WTC_IOC		= '1',
	WTC_GFS,
	WTC_GFD,
} WTSTimeCondition;

/*
 *
 */
typedef enum tagOrderFlag
{
	WOF_NOR = '0',
	WOF_FAK,
	WOF_FOK,
} WTSOrderFlag;

/*
 *
 */
typedef enum tagOffsetType
{
	WOT_OPEN			= '0',
	WOT_CLOSE,
	WOT_FORCECLOSE,
	WOT_CLOSETODAY,
	WOT_CLOSEYESTERDAY,
} WTSOffsetType;

/*
 *	ҵ
 */
typedef enum tagBusinessType
{
	BT_CASH		= '0',	//��ͨ����,
	BT_ETF		= '1',	//ETF����
	BT_EXECUTE	= '2',	//��Ȩ��Ȩ
	BT_QUOTE	= '3',	//��Ȩ����
	BT_FORQUOTE = '4',	//��Ȩѯ��
	BT_FREEZE	= '5',	//��Ȩ����
	BT_CREDIT	= '6',	//������ȯ
	BT_UNKNOWN			//δ֪ҵ������
} WTSBusinessType;

/*
 *	������������
 */
typedef enum tagActionFlag
{
	WAF_CANCEL			= '0',	//����
	WAF_MODIFY			= '3',	//�޸�
} WTSActionFlag;

/*
 *	����״̬
 */
typedef enum tagOrderState
{
	WOS_AllTraded				= '0',	//ȫ���ɽ�
	WOS_PartTraded_Queuing,				//���ֳɽ�,���ڶ�����
	WOS_PartTraded_NotQueuing,			//���ֳɽ�,δ�ڶ���
	WOS_NotTraded_Queuing,				//δ�ɽ�
	WOS_NotTraded_NotQueuing,			//δ�ɽ�,δ�ڶ���
	WOS_Canceled,						//�ѳ���
	WOS_Submitting				= 'a',	//�����ύ
	WOS_Cancelling,						//�ڳ�
	WOS_Nottouched,						//δ����
} WTSOrderState;

/*
 *	��������
 */
typedef enum tagOrderType
{
	WORT_Normal			= 0,		//��������
	WORT_Exception,					//�쳣����
	WORT_System,					//ϵͳ����
	WORT_Hedge						//�Գ嶩��
} WTSOrderType;

/*
 *	�ɽ�����
 */
typedef enum tagTradeType
{
	WTT_Common				= '0',	//��ͨ
	WTT_OptionExecution		= '1',	//��Ȩִ��
	WTT_OTC					= '2',	//OTC�ɽ�
	WTT_EFPDerived			= '3',	//��ת�������ɽ�
	WTT_CombinationDerived	= '4'	//��������ɽ�
} WTSTradeType;


/*
 *	�������
 */
typedef enum tagErrorCode
{
	WEC_NONE			=	0,		//û�д���
	WEC_ORDERINSERT,				//�µ�����
	WEC_ORDERCANCEL,				//��������
	WEC_EXECINSERT,					//��Ȩָ�����
	WEC_EXECCANCEL,					//��Ȩ��������
	WEC_UNKNOWN			=	9999	//δ֪����
} WTSErroCode;

/*
 *	�Ƚ��ֶ�
 */
typedef enum tagCompareField
{
	WCF_NEWPRICE			=	0,	//���¼�
	WCF_BIDPRICE,					//��һ��
	WCF_ASKPRICE,					//��һ��
	WCF_PRICEDIFF,					//�۲�,ֹӯֹ��ר��
	WCF_NONE				=	9	//���Ƚ�
} WTSCompareField;

/*
 *	�Ƚ�����
 */
typedef enum tagCompareType
{
	WCT_Equal			= 0,		//����
	WCT_Larger,						//����
	WCT_Smaller,					//С��
	WCT_LargerOrEqual,				//���ڵ���
	WCT_SmallerOrEqual				//С�ڵ���
}WTSCompareType;

/*
 *	����������¼�
 */
typedef enum tagParserEvent
{
	WPE_Connect			= 0,		//�����¼�
	WPE_Close,						//�ر��¼�
	WPE_Login,						//��¼
	WPE_Logout						//ע��
}WTSParserEvent;

/*
 *	����Դ�������¼�
 */
typedef enum tagThirdPartyDataEvent
{
    WTPDE_Connect			= 0,		//�����¼�
    WTPDE_Close,						//�ر��¼�
    WTPDE_Login,						//��¼
    WTPDE_Logout						//ע��
}WTSThirdPartyDataEvent;

/*
 *	����Դ�����ص��¼�
 */
typedef enum tagThirdPartyDataRedisEvent
{
    TPDR_Parameter			= 0,		//�����¼�
}WTSThirdPartyDataRedisEvent;

/*
 *	����ģ���¼�
 */
typedef enum tagTraderEvent
{
	WTE_Connect			= 0,		//�����¼�
	WTE_Close,						//�ر��¼�
	WTE_Login,						//��¼
	WTE_Logout						//ע��
}WTSTraderEvent;

/*
 *	���ݴ���ģ���¼�
 */
    typedef enum tagDataComputerEvent
    {
        WTDC_Connect			= 0,		//�����¼�
        WTDC_Close,						//�ر��¼�
        WTDC_Login,						//��¼
        WTDC_Logout						//ע��
    }WTSDataComputerEvent;

/*
 *	����״̬
 */
typedef enum tagTradeStatus
{
	TS_BeforeTrading	= '0',	//����ǰ
	TS_NotTrading		= '1',	//�ǽ���
	TS_Continous		= '2',	//��������
	TS_AuctionOrdering	= '3',	//���Ͼ����µ�
	TS_AuctionBalance	= '4',	//���Ͼ���ƽ��
	TS_AuctionMatch		= '5',	//���Ͼ��۴��
	TS_Closed			= '6'	//����
}WTSTradeStatus;

/*
 *	��Ϣ���ն�Ӧ����Ϣ����
 */
typedef enum messageType
{
    PSI_ADD	= 0,	//����
    PSI_RUN,		//����
    PSI_STOP,		//ֹͣ
    PSI_CANCEL,		//����
    PSI_MOD,		//�޸�
    PSI_DEL,		//ɾ��
}PSIMessageType;

/*
 *	������������
 */
typedef uint32_t WTSBSDirectType;
#define BDT_Buy		'B'	//����	
#define BDT_Sell	'S'	//����
#define BDT_Unknown ' '	//δ֪
#define BDT_Borrow	'G'	//����
#define BDT_Lend	'F'	//���

/*
 *	�ɽ�����
 */
typedef uint32_t WTSTransType;
#define TT_Unknown	'U'	//δ֪����
#define TT_Match	'M'	//��ϳɽ�
#define TT_Cancel	'C'	//����
#define TT_F_Match	'F'	//��ϳɽ�
#define TT_F_Unknown	'N'	//δ֪

/*
 *	ί����ϸ����
 */
typedef uint32_t WTSOrdDetailType;
#define ODT_Unknown		0	//δ֪����
#define ODT_BestPrice	'U'	//��������
#define ODT_AnyPrice	'1'	//�м�
#define ODT_LimitPrice	'2'	//�޼�
#define ODT_HomeBest	'3'	//��������

/*
 *	����״̬
 */
typedef uint32_t WTSOrderStatusType;
#define HX_LOS_Add  'A' //����
#define HX_LOS_Delete 'D' //ɾ��

typedef uint32_t PSITriggerType;
#define TT_Order		'0'	//ί�д���
#define TT_Transaction	'1'	//�ɽ�����

/////////////////////////////////////////////////////////////////////////
/// PSIStockRequestIDType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockRequestIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockLogInAccountType��һ����¼�˻�����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockLogInAccountType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockLogInAccountTypeType��һ����¼�˻���������
/////////////////////////////////////////////////////////////////////////
///�û�����
const char PSI_STOCK_LACT_UserID = '0';
///�ʽ��˺�
const char PSI_STOCK_LACT_AccountID = '1';
///�Ϻ�A��
const char PSI_STOCK_LACT_SHAStock = '2';
///����A��
const char PSI_STOCK_LACT_SZAStock = '3';
///�Ϻ�B��
const char PSI_STOCK_LACT_SHBStock = '4';
///����B��
const char PSI_STOCK_LACT_SZBStock = '5';
///����A
const char PSI_STOCK_LACT_ThreeNewBoardA = '6';
///����B
const char PSI_STOCK_LACT_ThreeNewBoardB = '7';
///�۹�
const char PSI_STOCK_LACT_HKStock = '8';
///ͳһ�û�����
const char PSI_STOCK_LACT_UnifiedUserID = '9';
///����A��
const char PSI_STOCK_LACT_BJAStock = 'a';
typedef char PSIStockLogInAccountTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockDepartmentIDType��һ�����Ŵ�������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockDepartmentIDType[11];

/////////////////////////////////////////////////////////////////////////
/// PSIStockAuthModeType��һ����֤��ʽ����
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_AM_Password = '0';
///ָ��
const char PSI_STOCK_AM_FingerPrint = '1';
///Կ�״�
const char PSI_STOCK_AM_CertInfo = '2';
typedef char PSIStockAuthModeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPasswordType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockPasswordType[41];

/////////////////////////////////////////////////////////////////////////
/// PSIStockUserProductInfoType��һ���û��˲�Ʒ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockUserProductInfoType[11];

/////////////////////////////////////////////////////////////////////////
/// PSIStockInterfaceProductInfoType��һ���ӿڶ˲�Ʒ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockInterfaceProductInfoType[33];

/////////////////////////////////////////////////////////////////////////
/// PSIStockTerminalInfoType��һ���ն���Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockTerminalInfoType[256];

/////////////////////////////////////////////////////////////////////////
/// PSIStockIPAddressType��һ��IP��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockIPAddressType[16];

/////////////////////////////////////////////////////////////////////////
/// PSIStockMacAddressType��һ��Mac��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockMacAddressType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockLangType��һ����������
/////////////////////////////////////////////////////////////////////////
///��������
const char PSI_STOCK_LGT_ZHCN = '0';
///�������
const char PSI_STOCK_LGT_ZHHK = '1';
///Ӣ������
const char PSI_STOCK_LGT_ENUS = '2';
typedef char PSIStockLangType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockDeviceIDType��һ���豸��ʶ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockDeviceIDType[129];

/////////////////////////////////////////////////////////////////////////
/// PSIStockCertSerialType��һ����֤��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockCertSerialType[129];

/////////////////////////////////////////////////////////////////////////
/// PSIStockDeviceTypeType��һ���豸�������
/////////////////////////////////////////////////////////////////////////
///PC��
const char PSI_STOCK_DVT_PC = '0';
///�ƶ���
const char PSI_STOCK_DVT_Mobile = '1';
typedef char PSIStockDeviceTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockNodeIDType��һ���ڵ�������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockNodeIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockFrontIDType��һ��ǰ�ñ������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockFrontIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockSessionIDType��һ���Ự�������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockSessionIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderRefType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockOrderRefType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockVolumeType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockVolumeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTimeType��һ��ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockTimeType[9];

/////////////////////////////////////////////////////////////////////////
/// PSIStockSystemNameType��һ��ϵͳ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockSystemNameType[41];

/////////////////////////////////////////////////////////////////////////
/// PSIStockDateType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockDateType[9];

/////////////////////////////////////////////////////////////////////////
/// PSIStockUserIDType��һ�������û���������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockUserIDType[16];

/////////////////////////////////////////////////////////////////////////
/// PSIStockUserNameType��һ���û���������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockUserNameType[81];

/////////////////////////////////////////////////////////////////////////
/// PSIStockUserTypeType��һ���û���������
/////////////////////////////////////////////////////////////////////////
///���͹�˾�û�
const char PSI_STOCK_UTYPE_BrokerUser = '0';
///�����û�
const char PSI_STOCK_UTYPE_SuperUser = '1';
///Ͷ�����û�
const char PSI_STOCK_UTYPE_Investor = '2';
typedef char PSIStockUserTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockCommFluxType��һ��ͨѶ��������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockCommFluxType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockBoolType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockBoolType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockErrorIDType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockErrorIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockErrorMsgType��һ��������Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockErrorMsgType[81];

/////////////////////////////////////////////////////////////////////////
/// PSIStockExchangeIDType��һ����������������
/////////////////////////////////////////////////////////////////////////
///ͨ��(�ڲ�ʹ��)
const char PSI_STOCK_EXD_COMM = '0';
///�Ϻ�������
const char PSI_STOCK_EXD_SSE = '1';
///���ڽ�����
const char PSI_STOCK_EXD_SZSE = '2';
///��۽�����
const char PSI_STOCK_EXD_HK = '3';
///����֤ȯ������
const char PSI_STOCK_EXD_BSE = '4';
typedef char PSIStockExchangeIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockInvestorIDType��һ��Ͷ���ߴ�������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockInvestorIDType[16];

/////////////////////////////////////////////////////////////////////////
/// PSIStockBusinessUnitIDType��һ��Ͷ�ʵ�Ԫ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockBusinessUnitIDType[16];

/////////////////////////////////////////////////////////////////////////
/// PSIStockShareholderIDType��һ���ɶ��˻���������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockShareholderIDType[11];

/////////////////////////////////////////////////////////////////////////
/// PSIStockSecurityIDType��һ��֤ȯ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockSecurityIDType[31];

/////////////////////////////////////////////////////////////////////////
/// PSIStockDirectionType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_D_Buy = '0';
///����
const char PSI_STOCK_D_Sell = '1';
///ETF�깺
const char PSI_STOCK_D_ETFPur = '2';
///ETF���
const char PSI_STOCK_D_ETFRed = '3';
///�¹��깺
const char PSI_STOCK_D_IPO = '4';
///���ع�
const char PSI_STOCK_D_Repurchase = '5';
///��ع�
const char PSI_STOCK_D_ReverseRepur = '6';
///����ʽ�����깺
const char PSI_STOCK_D_OeFundPur = '8';
///����ʽ�������
const char PSI_STOCK_D_OeFundRed = '9';
///����Ʒ����
const char PSI_STOCK_D_CollateralIn = 'a';
///����Ʒ����
const char PSI_STOCK_D_CollateralOut = 'b';
///��Ѻ���
const char PSI_STOCK_D_PledgeIn = 'd';
///��Ѻ����
const char PSI_STOCK_D_PledgeOut = 'e';
///�����ծ
const char PSI_STOCK_D_Rationed = 'f';
///������
const char PSI_STOCK_D_Split = 'g';
///����ϲ�
const char PSI_STOCK_D_Merge = 'h';
///��������
const char PSI_STOCK_D_CreditBuy = 'i';
///��ȯ����
const char PSI_STOCK_D_CreditSell = 'j';
///��ȯ����
const char PSI_STOCK_D_SellRepay = 'k';
///��ȯ��ȯ
const char PSI_STOCK_D_BuyRepay = 'l';
///��ȯ��ת
const char PSI_STOCK_D_RepayTransfer = 'm';
///��ȯ��ת
const char PSI_STOCK_D_SurplusTransfer = 'n';
///Դȯ��ת
const char PSI_STOCK_D_SourceTransfer = 'o';
///ծȯת��
const char PSI_STOCK_D_BondConvertStock = 't';
///ծȯ����
const char PSI_STOCK_D_BondPutback = 'u';
///ETFʵ���깺
const char PSI_STOCK_D_ETFOtPur = 'v';
///ETFʵ�����
const char PSI_STOCK_D_ETFOtRed = 'w';
///���۳���
const char PSI_STOCK_D_PutbackRelieve = 'x';
///��������
const char PSI_STOCK_D_IOIBuy = 'A';
///��������
const char PSI_STOCK_D_IOISell = 'B';
///�ɽ�ȷ������
const char PSI_STOCK_D_TCRBuy = 'C';
///�ɽ�ȷ������
const char PSI_STOCK_D_TCRSell = 'D';
typedef char PSIStockDirectionType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPriceType��һ���۸�����
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockPriceType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderPriceTypeType��һ�������۸���������
/////////////////////////////////////////////////////////////////////////
///�����
const char PSI_STOCK_OPT_AnyPrice = '1';
///�޼�
const char PSI_STOCK_OPT_LimitPrice = '2';
///���ż�
const char PSI_STOCK_OPT_BestPrice = '3';
///�̺󶨼�
const char PSI_STOCK_OPT_FixPrice = '4';
///�嵵��
const char PSI_STOCK_OPT_FiveLevelPrice = '5';
///��������
const char PSI_STOCK_OPT_HomeBestPrice = '6';
typedef char PSIStockOrderPriceTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTimeConditionType��һ����Ч����������
/////////////////////////////////////////////////////////////////////////
///������ɣ�������
const char PSI_STOCK_TC_IOC = '1';
///������Ч
const char PSI_STOCK_TC_GFS = '2';
///������Ч
const char PSI_STOCK_TC_GFD = '3';
///ָ������ǰ��Ч
const char PSI_STOCK_TC_GTD = '4';
///����ǰ��Ч
const char PSI_STOCK_TC_GTC = '5';
///���Ͼ�����Ч
const char PSI_STOCK_TC_GFA = '6';
typedef char PSIStockTimeConditionType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockVolumeConditionType��һ���ɽ�����������
/////////////////////////////////////////////////////////////////////////
///�κ�����
const char PSI_STOCK_VC_AV = '1';
///��С����
const char PSI_STOCK_VC_MV = '2';
///ȫ������
const char PSI_STOCK_VC_CV = '3';
typedef char PSIStockVolumeConditionType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOperwayType��һ��ί�з�ʽ����
/////////////////////////////////////////////////////////////////////////
///��
const char PSI_STOCK_OPERW_Non = ' ';
///�绰ί��
const char PSI_STOCK_OPERW_Telephone = '0';
///��̨ί��
const char PSI_STOCK_OPERW_OTC = '1';
///�ƶ��ͻ���ί��
const char PSI_STOCK_OPERW_MobileClient = '2';
///PC�ͻ���ί��
const char PSI_STOCK_OPERW_PCClient = '3';
///TYί��
const char PSI_STOCK_OPERW_TY = '4';
///ͨ��ί��
const char PSI_STOCK_OPERW_Channel = '5';
typedef char PSIStockOperwayType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockLotTypeType��һ���۹ɶ���������������
/////////////////////////////////////////////////////////////////////////
///��ɶ���
const char PSI_STOCK_LT_OddLot = '0';
///���ֶ���
const char PSI_STOCK_LT_RoundLot = '1';
typedef char PSIStockLotTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderSysIDType��һ��ϵͳ�����������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockOrderSysIDType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockCondCheckType��һ��ί�������������
/////////////////////////////////////////////////////////////////////////
///�����κμ��
const char PSI_STOCK_CCT_None = '0';
///�Գɽ����
const char PSI_STOCK_CCT_SelfDeal = '1';
typedef char PSIStockCondCheckType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockForceCloseReasonType��һ��ǿƽԭ������
/////////////////////////////////////////////////////////////////////////
///��ǿƽ
const char PSI_STOCK_FCC_NotForceClose = '0';
///�ʽ���
const char PSI_STOCK_FCC_MoneyNotEnough = '1';
///��λ����
const char PSI_STOCK_FCC_PositionOverFull = '2';
///����
const char PSI_STOCK_FCC_Other = '3';
typedef char PSIStockForceCloseReasonType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockCreditDebtIDType��һ�����ø�ծ�������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockCreditDebtIDType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockCreditQuotaTypeType��һ������ͷ����������
/////////////////////////////////////////////////////////////////////////
///��ͨ
const char PSI_STOCK_CQT_Normal = '0';
///ר��
const char PSI_STOCK_CQT_Special = '1';
typedef char PSIStockCreditQuotaTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockIntSerialType��һ��������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockIntSerialType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockStrInfoType��һ���ַ���������Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockStrInfoType[33];

/////////////////////////////////////////////////////////////////////////
/// PSIStockIntInfoType��һ�����θ�����Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockIntInfoType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderLocalIDType��һ�����ر����������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockOrderLocalIDType[13];

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///�µ��ɹ�
const char PSI_STOCK_OST_Cached = '0';
///δ֪
const char PSI_STOCK_OST_Unknown = '1';
///�������ѽ���
const char PSI_STOCK_OST_Accepted = '2';
///���ֳɽ�
const char PSI_STOCK_OST_PartTraded = '3';
///ȫ���ɽ�
const char PSI_STOCK_OST_AllTraded = '4';
///���ɲ���
const char PSI_STOCK_OST_PartTradeCanceled = '5';
///ȫ������
const char PSI_STOCK_OST_AllCanceled = '6';
///�������Ѿܾ�
const char PSI_STOCK_OST_Rejected = '7';
///�������׺���
const char PSI_STOCK_OST_SendTradeEngine = '#';
typedef char PSIStockOrderStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderSubmitStatusType��һ�������ύ״̬����
/////////////////////////////////////////////////////////////////////////
///δ�ύ
const char PSI_STOCK_OSS_InsertUnSubmit = '0';
///���ύ
const char PSI_STOCK_OSS_InsertSubmitted = '1';
///����δ�ύ
const char PSI_STOCK_OSS_CancelUnSubmit = '2';
///�������ύ
const char PSI_STOCK_OSS_CancelSubmitted = '3';
///�����ѱ��ܾ�
const char PSI_STOCK_OSS_CancelRejected = '4';
///������ɾ��
const char PSI_STOCK_OSS_CancelDeleted = '5';
typedef char PSIStockOrderSubmitStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockAccountIDType��һ���ʽ��ʺ�����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockAccountIDType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockCurrencyIDType��һ����������
/////////////////////////////////////////////////////////////////////////
///�����
const char PSI_STOCK_CID_CNY = '1';
///�۱�
const char PSI_STOCK_CID_HKD = '2';
///��Ԫ
const char PSI_STOCK_CID_USD = '3';
typedef char PSIStockCurrencyIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPbuIDType��һ�����׵�Ԫ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockPbuIDType[11];

/////////////////////////////////////////////////////////////////////////
/// PSIStockMoneyType��һ���ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockMoneyType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_ORDT_Normal = '0';
///��������
const char PSI_STOCK_ORDT_DeriveFromQuote = '1';
///�������
const char PSI_STOCK_ORDT_DeriveFromCombination = '2';
///��ϱ���
const char PSI_STOCK_ORDT_Combination = '3';
///������
const char PSI_STOCK_ORDT_ConditionalOrder = '4';
///������
const char PSI_STOCK_ORDT_Swap = '5';
///Ԥ��
const char PSI_STOCK_ORDT_Cache = '6';
///ҹ��ί��
const char PSI_STOCK_ORDT_Night = '7';
///ͨ��ί��
const char PSI_STOCK_ORDT_Board = '8';
typedef char PSIStockOrderTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockQuotaIDType��һ����ȱ������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockQuotaIDType[17];

/////////////////////////////////////////////////////////////////////////
/// PSIStockFloatInfoType��һ�������͸�����Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockFloatInfoType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTradeIDType��һ���ɽ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockTradeIDType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockActionFlagType��һ��������־����
/////////////////////////////////////////////////////////////////////////
///ɾ��
const char PSI_STOCK_AF_Delete = '0';
///�޸�
const char PSI_STOCK_AF_Modify = '3';
///ǿ��ɾ��
const char PSI_STOCK_AF_ForceDelete = '4';
typedef char PSIStockActionFlagType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockCondOrderIDType��һ�����������������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockCondOrderIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTriggerOrderVolumeTypeType��һ��������׼������������
/////////////////////////////////////////////////////////////////////////
///�Զ�������
const char PSI_STOCK_TOVT_CustomVol = '1';
///�������
const char PSI_STOCK_TOVT_RelativeVol = '2';
typedef char PSIStockTriggerOrderVolumeTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTriggerOrderPriceTypeType��һ��������׼�۸���������
/////////////////////////////////////////////////////////////////////////
///�Զ���۸�
const char PSI_STOCK_TOPT_CustomPrice = '1';
///���¼�
const char PSI_STOCK_TOPT_LastPrice = '2';
///��һ��
const char PSI_STOCK_TOPT_AskPrice1 = '3';
///��һ��
const char PSI_STOCK_TOPT_BidPrice1 = '4';
///��ؼ�
const char PSI_STOCK_TOPT_Relative = '5';
typedef char PSIStockTriggerOrderPriceTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockContingentConditionType��һ��������������
/////////////////////////////////////////////////////////////////////////
///�ɽ�����
const char PSI_STOCK_CC_TradeTouch = '0';
///��������
const char PSI_STOCK_CC_CancelTouch = '1';
///ʱ�䴥��
const char PSI_STOCK_CC_TimeTouch = '2';
///����ʱ�δ���
const char PSI_STOCK_CC_SegmentTouch = '3';
///���¼۴��ڵ���������
const char PSI_STOCK_CC_LastPriceGreaterThanStopPrice = '4';
///���¼�С�ڵ���������
const char PSI_STOCK_CC_LastPriceLesserThanStopPrice = '5';
///��һ�۴��ڵ���������
const char PSI_STOCK_CC_AskPriceGreaterEqualStopPrice = '6';
///��һ��С�ڵ���������
const char PSI_STOCK_CC_AskPriceLesserEqualStopPrice = '7';
///��һ�۴��ڵ���������
const char PSI_STOCK_CC_BidPriceGreaterEqualStopPrice = '8';
///��һ��С�ڵ���������
const char PSI_STOCK_CC_BidPriceLesserEqualStopPrice = '9';
typedef char PSIStockContingentConditionType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockVolumeMultipleType��һ����Լ������������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockVolumeMultipleType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockRelativeCondParamType��һ�����������������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockRelativeCondParamType[31];

/////////////////////////////////////////////////////////////////////////
/// PSIStockCondOrderStatusType��һ��������״̬����
/////////////////////////////////////////////////////////////////////////
///��ʼ
const char PSI_STOCK_COST_Initial = '#';
///δ����
const char PSI_STOCK_COST_NotTouched = '0';
///�Ѵ���
const char PSI_STOCK_COST_Touched = '1';
///�ѽ���
const char PSI_STOCK_COST_Finished = '2';
///�ѳ���
const char PSI_STOCK_COST_Cancel = '3';
///����ʧ��
const char PSI_STOCK_COST_Failed = '4';
typedef char PSIStockCondOrderStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockNegoContractorType��һ����ϵ������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockNegoContractorType[17];

/////////////////////////////////////////////////////////////////////////
/// PSIStockNegoContractorInfoType��һ����ϵ��ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockNegoContractorInfoType[65];

/////////////////////////////////////////////////////////////////////////
/// PSIStockNegoConfirmIDType��һ��Լ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockNegoConfirmIDType[17];

/////////////////////////////////////////////////////////////////////////
/// PSIStockMarketIDType��һ���г���������
/////////////////////////////////////////////////////////////////////////
///ͨ��(�ڲ�ʹ��)
const char PSI_STOCK_MKD_COMMON = '0';
///�Ϻ�A��
const char PSI_STOCK_MKD_SHA = '1';
///����A��
const char PSI_STOCK_MKD_SZA = '2';
///�Ϻ�B��
const char PSI_STOCK_MKD_SHB = '3';
///����B��
const char PSI_STOCK_MKD_SZB = '4';
///��������A��
const char PSI_STOCK_MKD_SZThreeA = '5';
///��������B��
const char PSI_STOCK_MKD_SZThreeB = '6';
///�����г�
const char PSI_STOCK_MKD_Foreign = '7';
///���ڸ۹�ͨ�г�
const char PSI_STOCK_MKD_SZHK = '8';
///�Ϻ��۹�ͨ�г�
const char PSI_STOCK_MKD_SHHK = '9';
///��������
const char PSI_STOCK_MKD_BJMain = 'a';
typedef char PSIStockMarketIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockMarketStatusType��һ���г�״̬����
/////////////////////////////////////////////////////////////////////////
///δ֪
const char PSI_STOCK_MST_UnKnown = '#';
///����ǰ
const char PSI_STOCK_MST_BeforeTrading = '0';
///��������
const char PSI_STOCK_MST_Continous = '1';
///����
const char PSI_STOCK_MST_Closed = '2';
///���̼��Ͼ���
const char PSI_STOCK_MST_OpenCallAuction = '3';
///(�۹�ͨ)δ����
const char PSI_STOCK_MST_SZSEHKUnopened = 'a';
///(�۹�ͨ)���̼��Ͼ�������������
const char PSI_STOCK_MST_SZSEHKOpenCallAuctionInput = 'b';
///(�۹�ͨ)���̼��Ͼ��۶���ǰ
const char PSI_STOCK_MST_SZSEHKOpenCallAuctionBeforeMatch = 'c';
///(�۹�ͨ)���̼��Ͼ��۶���
const char PSI_STOCK_MST_SZSEHKOpenCallAuctionMatch = 'd';
///(�۹�ͨ)��ͣ
const char PSI_STOCK_MST_SZSEHKHalt = 'e';
///(�۹�ͨ)��������
const char PSI_STOCK_MST_SZSEHKContinous = 'f';
///(�۹�ͨ)Exchange Intervention
const char PSI_STOCK_MST_SZSEHKExchangeIntervention = 'g';
///(�۹�ͨ)���̼��Ͼ��۲ο��۶���
const char PSI_STOCK_MST_SZSEHKCloseCallAuctionReferencePrice = 'h';
///(�۹�ͨ)���̼��Ͼ�������������
const char PSI_STOCK_MST_SZSEHKCloseCallAuctionInput = 'i';
///(�۹�ͨ)���̼��Ͼ��۲���ȡ��
const char PSI_STOCK_MST_SZSEHKCloseCallAuctionCannotCancel = 'j';
///(�۹�ͨ)���̼��Ͼ��۶���
const char PSI_STOCK_MST_SZSEHKCloseCallAuctionMatch = 'k';
///(�۹�ͨ)���̼��Ͼ����������
const char PSI_STOCK_MST_SZSEHKCloseCallAuctionRandomClosed = 'l';
///(�۹�ͨ)ȡ��������
const char PSI_STOCK_MST_SZSEHKCancel = 'm';
///(�۹�ͨ)����
const char PSI_STOCK_MST_SZSEHKClosed = 'n';
///(�۹�ͨ)ȫ������
const char PSI_STOCK_MST_SZSEHKWholeClosed = 'o';
typedef char PSIStockMarketStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTransferDirectionType��һ��ת�Ʒ�������
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_TRNSD_In = '0';
///���
const char PSI_STOCK_TRNSD_Out = '1';
///���н��׵���
const char PSI_STOCK_TRNSD_MoveIn = '2';
///���н��׵���
const char PSI_STOCK_TRNSD_MoveOut = '3';
///����
const char PSI_STOCK_TRNSD_Freeze = '4';
///�ⶳ
const char PSI_STOCK_TRNSD_UnFreeze = '5';
///֤ȯת����
const char PSI_STOCK_TRNSD_StockToBank = '6';
///����ת֤ȯ
const char PSI_STOCK_TRNSD_BankToStock = '7';
///�ⲿ�ڵ�ת��
const char PSI_STOCK_TRNSD_NodeMoveIn = 'c';
///�ⲿ�ڵ�ת��
const char PSI_STOCK_TRNSD_NodeMoveOut = 'd';
///ֱ�ӻ���
const char PSI_STOCK_TRNSD_CashRepay = 'f';
///ֱ�ӻ�Ϣ(�ڲ�ʹ��)
const char PSI_STOCK_TRNSD_CashRepayInterestFee = 'g';
typedef char PSIStockTransferDirectionType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockBankIDType��һ�����д�������
/////////////////////////////////////////////////////////////////////////
///�й���������
const char PSI_STOCK_BKID_CCB = '1';
///�й�ũҵ����
const char PSI_STOCK_BKID_ABC = '2';
///�й���������
const char PSI_STOCK_BKID_ICBC = '3';
///�й�����
const char PSI_STOCK_BKID_BOC = '4';
///�й���������
const char PSI_STOCK_BKID_CMB = '5';
///�й���ͨ����
const char PSI_STOCK_BKID_BC = '6';
///�ֶ���չ����
const char PSI_STOCK_BKID_SPDB = '7';
///��ҵ����
const char PSI_STOCK_BKID_CIB = '8';
///�й��������
const char PSI_STOCK_BKID_CEB = '9';
///�㶫��չ����
const char PSI_STOCK_BKID_GDB = 'a';
///�Ͼ�����
const char PSI_STOCK_BKID_NJCB = 'b';
///�Ϻ�����
const char PSI_STOCK_BKID_SHCB = 'c';
///��������
const char PSI_STOCK_BKID_CITICB = 'd';
///��������
const char PSI_STOCK_BKID_HXB = 'e';
///��������
const char PSI_STOCK_BKID_CMBC = 'f';
///ƽ������
const char PSI_STOCK_BKID_PACB = 'g';
///��������
const char PSI_STOCK_BKID_NBCB = 'h';
///��������
const char PSI_STOCK_BKID_BOB = 'i';
///�ʴ�����
const char PSI_STOCK_BKID_PSBC = 'j';
typedef char PSIStockBankIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTransferStatusType��һ��ת��״̬����
/////////////////////////////////////////////////////////////////////////
///ת�����ڴ���
const char PSI_STOCK_TRANST_TranferHandling = '0';
///ת�Ƴɹ�
const char PSI_STOCK_TRANST_TransferSuccess = '1';
///ת��ʧ��
const char PSI_STOCK_TRANST_TransferFail = '2';
///�������ڴ���
const char PSI_STOCK_TRANST_RepealHandling = '3';
///�����ɹ�
const char PSI_STOCK_TRANST_RepealSuccess = '4';
///����ʧ��
const char PSI_STOCK_TRANST_RepealFail = '5';
///�ⲿϵͳ�ѽ���
const char PSI_STOCK_TRANST_ExternalAccepted = '6';
///�������׺���
const char PSI_STOCK_TRANST_SendTradeEngine = '#';
typedef char PSIStockTransferStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockBankAccountIDType��һ��ǩԼ�����˺�����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockBankAccountIDType[31];

/////////////////////////////////////////////////////////////////////////
/// PSIStockTransferPositionTypeType��һ��ת�Ƴֲ���������
/////////////////////////////////////////////////////////////////////////
///�����
const char PSI_STOCK_TPT_ALL = '0';
///���
const char PSI_STOCK_TPT_History = '1';
///��������
const char PSI_STOCK_TPT_TodayBS = '2';
///�������
const char PSI_STOCK_TPT_TodayPR = '3';
///���ֺϲ���
const char PSI_STOCK_TPT_TodaySM = '4';
typedef char PSIStockTransferPositionTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTransferReasonType��һ����Χϵͳ��λ����ԭ������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockTransferReasonType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockContentType��һ����Ϣ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockContentType[501];

/////////////////////////////////////////////////////////////////////////
/// PSIStockRatioType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockRatioType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockNameType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockNameType[61];

/////////////////////////////////////////////////////////////////////////
/// PSIStockDataSyncStatusType��һ������ͬ��״̬����
/////////////////////////////////////////////////////////////////////////
///δͬ��
const char PSI_STOCK_DS_Asynchronous = '1';
///ͬ����
const char PSI_STOCK_DS_Synchronizing = '2';
///��ͬ��
const char PSI_STOCK_DS_Synchronized = '3';
///ȫ��ͬ�����
const char PSI_STOCK_DS_AllSynchronized = '4';
///Ԥͬ�����
const char PSI_STOCK_DS_PreSync = '5';
typedef char PSIStockDataSyncStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockProductIDType��һ��֤ȯƷ�ִ�������
/////////////////////////////////////////////////////////////////////////
///ͨ��(�ڲ�ʹ��)
const char PSI_STOCK_PID_COMMON = '0';
///�Ϻ���Ʊ
const char PSI_STOCK_PID_SHStock = '1';
///�Ϻ�����
const char PSI_STOCK_PID_SHFund = '3';
///�Ϻ�ծȯ
const char PSI_STOCK_PID_SHBond = '4';
///�Ϻ���׼ȯ
const char PSI_STOCK_PID_SHStandard = '5';
///�Ϻ���Ѻʽ�ع�
const char PSI_STOCK_PID_SHRepurchase = '6';
///���ڹ�Ʊ
const char PSI_STOCK_PID_SZStock = '7';
///���ڻ���
const char PSI_STOCK_PID_SZFund = '9';
///����ծȯ
const char PSI_STOCK_PID_SZBond = 'a';
///���ڱ�׼ȯ
const char PSI_STOCK_PID_SZStandard = 'b';
///������Ѻʽ�ع�
const char PSI_STOCK_PID_SZRepurchase = 'c';
///���ͨ�۹�����
const char PSI_STOCK_PID_SZSEHKMain = 'd';
///���ͨ�۹ɴ�ҵ��
const char PSI_STOCK_PID_SZSEHKGEM = 'e';
///���ͨ�۹����佻��֤ȯ
const char PSI_STOCK_PID_SZSEHKETS = 'f';
///���ͨ�۹�NasdaqAMX�г�
const char PSI_STOCK_PID_SZSEHKNasdaqAMX = 'g';
///�Ϻ��ƴ���
const char PSI_STOCK_PID_SHKC = 'i';
///������Ʊ
const char PSI_STOCK_PID_BJStock = 'j';
typedef char PSIStockProductIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockSecurityNameType��һ��֤ȯ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockSecurityNameType[81];

/////////////////////////////////////////////////////////////////////////
/// PSIStockSecurityTypeType��һ��֤ȯ�������
/////////////////////////////////////////////////////////////////////////
///ͨ��(�ڲ�ʹ��)
const char PSI_STOCK_STP_COMMON = '0';
///�Ϻ�A��
const char PSI_STOCK_STP_SHAShares = 'a';
///�Ϻ����г���ƱETF
const char PSI_STOCK_STP_SHSingleMarketStockETF = 'b';
///�Ϻ����г�ʵ��ծȯETF
const char PSI_STOCK_STP_SHSingleMarketBondETF = 'c';
///�Ϻ��ƽ�ETF
const char PSI_STOCK_STP_SHGoldETF = 'd';
///�Ϻ�����ETF
const char PSI_STOCK_STP_SHTradableMonetaryFund = 'e';
///�Ϻ���ծ�ط�ծ
const char PSI_STOCK_STP_SHBondNation = 'f';
///�Ϻ���ҵծ
const char PSI_STOCK_STP_SHBondCorporation = 'g';
///�Ϻ���˾ծ
const char PSI_STOCK_STP_SHBondCompany = 'h';
///�Ϻ���תծ
const char PSI_STOCK_STP_SHBondConversion = 'i';
///�Ϻ�����ծ
const char PSI_STOCK_STP_SHBondSeparation = 'j';
///�Ϻ���׼ȯ
const char PSI_STOCK_STP_SHStandard = 'o';
///�Ϻ���Ѻʽ�ع�
const char PSI_STOCK_STP_SHRepo = 'p';
///�Ϻ����ʽ����
const char PSI_STOCK_STP_SHCEFund = 'q';
///�Ϻ�����ʽ����
const char PSI_STOCK_STP_SHOEFund = 'r';
///�Ϻ����г�ETF
const char PSI_STOCK_STP_SHCrossMarketStockETF = 's';
///�Ϻ��羳ETF
const char PSI_STOCK_STP_SHCrossBorderETF = 't';
///�Ϻ��ּ�ĸ����
const char PSI_STOCK_STP_SHMontherStructFund = 'u';
///�Ϻ��ּ��ӻ���
const char PSI_STOCK_STP_SHSubStructFund = 'v';
///�Ϻʵʱһ
const char PSI_STOCK_STP_SHRealTimeMonetaryFund = 'w';
///�Ϻ��ɽ���ծ
const char PSI_STOCK_STP_SHExchangeableBond = 'x';
///�Ϻ���׼LOF����
const char PSI_STOCK_STP_SHLOF = 'A';
///��������A��
const char PSI_STOCK_STP_SZMainAShares = 'B';
///������С��ҵ��
const char PSI_STOCK_STP_SZSME = 'C';
///���ڹ�ծ���ط�ծ
const char PSI_STOCK_STP_SZBondNation = 'D';
///������ҵծ
const char PSI_STOCK_STP_SZBondCorporation = 'E';
///���ڹ�˾ծ
const char PSI_STOCK_STP_SZBondCompany = 'F';
///���ڿ�תծ
const char PSI_STOCK_STP_SZBondConversion = 'G';
///���ڷ���ծ
const char PSI_STOCK_STP_SZBondSeparation = 'H';
///���ڴ�ҵ��(ע����)
const char PSI_STOCK_STP_SZGEMReg = 'I';
///���ڴ�ҵ���תծ(ע����)
const char PSI_STOCK_STP_SZGEMBondConversionReg = 'J';
///���ڿ羳ETF
const char PSI_STOCK_STP_SZCrossBorderETF = 'K';
///���ڻƽ�ETF
const char PSI_STOCK_STP_SZGoldETF = 'L';
///�����ֽ�ծȯETF
const char PSI_STOCK_STP_SZCashBondETF = 'M';
///���ڵ��г���ƱETF
const char PSI_STOCK_STP_SZSingleMarketStockETF = 'N';
///���ڵ��г�ʵ��ծȯETF
const char PSI_STOCK_STP_SZSingleMarketBondETF = 'O';
///���ڻ���ETF
const char PSI_STOCK_STP_SZMonetaryFundETF = 'P';
///���ڴ�ҵ��
const char PSI_STOCK_STP_SZGEM = 'Q';
///���ڴ�ҵ��ɽ���ծ
const char PSI_STOCK_STP_SZGEMExchangeableBond = 'R';
///���ڴ�ҵ��ɽ���ծ(ע����)
const char PSI_STOCK_STP_SZGEMExchangeableBondReg = 'S';
///���ڱ�׼ȯ
const char PSI_STOCK_STP_SZStandard = 'T';
///������Ѻʽ�ع�
const char PSI_STOCK_STP_SZRepo = 'U';
///���ڷ��ʽ����
const char PSI_STOCK_STP_SZCEFund = 'V';
///���ڿ���ʽ����
const char PSI_STOCK_STP_SZOEFund = 'W';
///���ڿ羳����ʽ����
const char PSI_STOCK_STP_SZCrossBorderOEFund = 'X';
///���ڿ��г���ƱETF
const char PSI_STOCK_STP_SZCrossMarketStockETF = 'Y';
///���ڱ�׼LOF����
const char PSI_STOCK_STP_SZLOF = 'Z';
///���ڿ羳LOF����
const char PSI_STOCK_STP_SZCrossBorderLOF = '1';
///���ڴ�ͳ�ּ�ĸ����
const char PSI_STOCK_STP_SZMontherStructFund = '2';
///���ڴ�ͳ�ּ��ӻ���
const char PSI_STOCK_STP_SZSubStructFund = '3';
///���ڿ羳�ּ�ĸ����
const char PSI_STOCK_STP_SZMontherCrossBorderStructFund = '4';
///���ڿ羳�ּ��ӻ���
const char PSI_STOCK_STP_SZSubCrossBorderStructFund = '5';
///���ڿɽ���ծ
const char PSI_STOCK_STP_SZExchangeableBond = '6';
///���ڴ�ҵ���תծ
const char PSI_STOCK_STP_SZGEMBondConversion = '7';
///���ͨ�۹�ծȯ
const char PSI_STOCK_STP_SZSEHKBond = '8';
///���ͨ�۹�һ����Ȩ֤
const char PSI_STOCK_STP_SZSEHKBasketWarrant = '9';
///���ͨ�۹ɹɱ�
const char PSI_STOCK_STP_SZSEHKEquity = 'y';
///���ͨ�۹�����
const char PSI_STOCK_STP_SZSEHKTrust = 'z';
///���ͨ�۹�Ȩ֤
const char PSI_STOCK_STP_SZSEHKWarrant = '#';
///�Ϻ�����ƾ֤
const char PSI_STOCK_STP_SHCDR = '+';
///�Ϻ��ƴ����Ʊ
const char PSI_STOCK_STP_SHKC = '*';
///�ƴ����Ʒ�����к�ǰ5�������գ�
const char PSI_STOCK_STP_SHKC1 = '^';
///�Ϻ��ƴ������ƾ֤
const char PSI_STOCK_STP_SHKCCDR = '-';
///�������塢��С�崴����ҵ��Ʊ�����ƾ֤
const char PSI_STOCK_STP_SZCDR = 'k';
///���ڴ�ҵ�崴����ҵ��Ʊ�����ƾ֤
const char PSI_STOCK_STP_SZGEMCDR = 'l';
///���ڴ�ҵ�崴����ҵ��Ʊ�����ƾ֤(ע����)
const char PSI_STOCK_STP_SZGEMCDRReg = 'm';
///������Ʒ�ڻ�ETF
const char PSI_STOCK_STP_SZCommFuturesETF = 'n';
///���ڻ�����ʩ����
const char PSI_STOCK_STP_SZInfrastructureFund = '=';
///�Ϻ��ƴ���ETF
const char PSI_STOCK_STP_SHKCETF = '@';
///�Ϻ��ƴ���LOF
const char PSI_STOCK_STP_SHKCLOF = '%';
///�Ϻ��ƴ����תծ
const char PSI_STOCK_STP_SHKCBondConversion = '$';
///�Ϻ������תծ
const char PSI_STOCK_STP_SHOrientedConversionBond = '<';
///���ڶ����תծ
const char PSI_STOCK_STP_SZOrientedConversionBond = '>';
///�Ϻ�������ʩ����
const char PSI_STOCK_STP_SHInfrastructureFund = '~';
///������Ʊ
const char PSI_STOCK_STP_BJStock = '[';
///�Ϻ��ƴ���ɽ���ծ
const char PSI_STOCK_STP_SHKCExchangeableBond = ']';
typedef char PSIStockSecurityTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOrderUnitType��һ���걨��λ����
/////////////////////////////////////////////////////////////////////////
///��
const char PSI_STOCK_OUT_Shou = '0';
///��
const char PSI_STOCK_OUT_Gu = '1';
///��
const char PSI_STOCK_OUT_Fen = '2';
///��
const char PSI_STOCK_OUT_Zhang = '3';
typedef char PSIStockOrderUnitType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTradingUnitType��һ�����׵�λ����
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockTradingUnitType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPriceTickType��һ����С�䶯��λ����
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockPriceTickType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockParValueType��һ����ֵ����
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockParValueType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockSecurityStatusType��һ��֤ȯ״̬����
/////////////////////////////////////////////////////////////////////////
#ifdef WINDOWS
typedef __int64 PSIStockSecurityStatusType;
#else
typedef long long int PSIStockSecurityStatusType;
#endif

/////////////////////////////////////////////////////////////////////////
/// PSIStockInterestType��һ����Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockInterestType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockLargeVolumeType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef double PSIStockLargeVolumeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockIssueModeType��һ�����з�ʽ����
/////////////////////////////////////////////////////////////////////////
///��ֵ���۷�ʽ
const char PSI_STOCK_IMO_ValueLimit = '0';
///�������۷�ʽ
const char PSI_STOCK_IMO_AddIssue = '1';
///�����깺��ʽ
const char PSI_STOCK_IMO_Credit = '2';
///���۷���(������)
const char PSI_STOCK_IMO_Fixed = '3';
///���۷���(������)
const char PSI_STOCK_IMO_Auction = '4';
typedef char PSIStockIssueModeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockLoginLimitType��һ����¼��������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockLoginLimitType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockLoginStatusType��һ����¼״̬����
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_USTS_Enabled = '1';
///����
const char PSI_STOCK_USTS_Disabled = '2';
///����
const char PSI_STOCK_USTS_Locked = '4';
typedef char PSIStockLoginStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockInvestorTypeType��һ��Ͷ������������
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_CT_Person = '0';
///����
const char PSI_STOCK_CT_Company = '1';
///��Ӫ
const char PSI_STOCK_CT_SelfOperate = '5';
///����
const char PSI_STOCK_CT_Test = '#';
typedef char PSIStockInvestorTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockInvestorNameType��һ��Ͷ������������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockInvestorNameType[81];

/////////////////////////////////////////////////////////////////////////
/// PSIStockIdCardTypeType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
///��֯��������
const char PSI_STOCK_ICT_EID = '0';
///�й���������֤
const char PSI_STOCK_ICT_IDCard = '1';
///����֤
const char PSI_STOCK_ICT_OfficerIDCard = '2';
///����֤
const char PSI_STOCK_ICT_PoliceIDCard = '3';
///ʿ��֤
const char PSI_STOCK_ICT_SoldierIDCard = '4';
///���ڲ�
const char PSI_STOCK_ICT_HouseholdRegister = '5';
///����
const char PSI_STOCK_ICT_Passport = '6';
///̨��֤
const char PSI_STOCK_ICT_TaiwanCompatriotIDCard = '7';
///����֤
const char PSI_STOCK_ICT_HomeComingCard = '8';
///Ӫҵִ�պ�
const char PSI_STOCK_ICT_LicenseNo = '9';
///˰��ǼǺ�/������˰ID
const char PSI_STOCK_ICT_TaxNo = 'A';
///�۰ľ��������ڵ�ͨ��֤
const char PSI_STOCK_ICT_HMMainlandTravelPermit = 'B';
///̨�����������½ͨ��֤
const char PSI_STOCK_ICT_TwMainlandTravelPermit = 'C';
///����
const char PSI_STOCK_ICT_DrivingLicense = 'D';
///�����籣ID
const char PSI_STOCK_ICT_SocialID = 'F';
///��������֤
const char PSI_STOCK_ICT_LocalID = 'G';
///��ҵ�Ǽ�֤
const char PSI_STOCK_ICT_BusinessRegistration = 'H';
///�۰������Ծ�������֤
const char PSI_STOCK_ICT_HKMCIDCard = 'I';
///���п�������֤
const char PSI_STOCK_ICT_AccountsPermits = 'J';
///����֤��
const char PSI_STOCK_ICT_OtherCard = 'x';
typedef char PSIStockIdCardTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockIdCardNoType��һ��֤���������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockIdCardNoType[51];

/////////////////////////////////////////////////////////////////////////
/// PSIStockTradingStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_TS_Normal = '1';
///����ǿƽ
const char PSI_STOCK_TS_ForceClosing = '2';
///�쳣
const char PSI_STOCK_TS_Exception = '3';
typedef char PSIStockTradingStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockOperwaysType��һ��ί�з�ʽ�ϼ�����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockOperwaysType[41];

/////////////////////////////////////////////////////////////////////////
/// PSIStockMobileType��һ���ֻ�����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockMobileType[41];

/////////////////////////////////////////////////////////////////////////
/// PSIStockTelephoneType��һ����ϵ�绰����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockTelephoneType[41];

/////////////////////////////////////////////////////////////////////////
/// PSIStockEmailType��һ��Email����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockEmailType[61];

/////////////////////////////////////////////////////////////////////////
/// PSIStockFaxType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockFaxType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockAddressType��һ��ͨѶ��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockAddressType[101];

/////////////////////////////////////////////////////////////////////////
/// PSIStockZipCodeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockZipCodeType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockProfInvestorTypeType��һ��רҵͶ�����������
/////////////////////////////////////////////////////////////////////////
///��רҵͶ����
const char PSI_STOCK_PIT_Normal = '0';
///רҵͶ����
const char PSI_STOCK_PIT_Professional = '1';
typedef char PSIStockProfInvestorTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPlanTypeType��һ���ײ���������
/////////////////////////////////////////////////////////////////////////
///��׼�ײ�
const char PSI_STOCK_PLTP_Standard = '0';
///�����ײ�
const char PSI_STOCK_PLTP_Smart = '1';
///Ԥ��1
const char PSI_STOCK_PLTP_Reserve1 = '2';
///Ԥ��2
const char PSI_STOCK_PLTP_Reserve2 = '3';
///δ�������ͣ��ڲ�ʹ�ã�
const char PSI_STOCK_PLTP_Undefined = '4';
typedef char PSIStockPlanTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockRemarkType��һ����ע����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockRemarkType[513];

/////////////////////////////////////////////////////////////////////////
/// PSIStockShareholderIDTypeType��һ���ɶ��˻���������
/////////////////////////////////////////////////////////////////////////
///Ͷ��
const char PSI_STOCK_SIDT_Speculation = '1';
///����
const char PSI_STOCK_SIDT_Arbitrage = '2';
///�ױ�
const char PSI_STOCK_SIDT_Hedge = '3';
///��ͨ
const char PSI_STOCK_SIDT_Normal = 'a';
///����
const char PSI_STOCK_SIDT_Credit = 'b';
///����Ʒ
const char PSI_STOCK_SIDT_Derivatives = 'c';
typedef char PSIStockShareholderIDTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockCancelOrderStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///Ԥ��
const char PSI_STOCK_CORDS_Cached = '0';
///���ύ
const char PSI_STOCK_CORDS_Submitted = '1';
///�ɹ�
const char PSI_STOCK_CORDS_Success = '2';
///�ܾ�
const char PSI_STOCK_CORDS_Rejected = '3';
typedef char PSIStockCancelOrderStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockCancelOrderTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��ͨ����
const char PSI_STOCK_CORDT_Normal = '0';
///ǿ�Ƴ���
const char PSI_STOCK_CORDT_Force = '1';
///Ԥ�񳷵�
const char PSI_STOCK_CORDT_Cache = '2';
///ͨ������
const char PSI_STOCK_CORDT_Board = '3';
typedef char PSIStockCancelOrderTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockAccountTypeType��һ���ʽ��˻���������
/////////////////////////////////////////////////////////////////////////
///��ͨ
const char PSI_STOCK_FAT_Normal = '1';
///����
const char PSI_STOCK_FAT_Credit = '2';
///����Ʒ
const char PSI_STOCK_FAT_Derivatives = '3';
typedef char PSIStockAccountTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockBizClassType��һ��ҵ���������
/////////////////////////////////////////////////////////////////////////
///����
const char PSI_STOCK_BC_Buy = '0';
///����
const char PSI_STOCK_BC_Sell = '1';
///ETF�깺
const char PSI_STOCK_BC_ETFPur = '2';
///ETF���
const char PSI_STOCK_BC_ETFRed = '3';
///�¹��깺
const char PSI_STOCK_BC_SubscribingShares = '4';
///���ع�
const char PSI_STOCK_BC_Repurchase = '5';
///��ع�
const char PSI_STOCK_BC_ReverseRepur = '6';
///����ʽ�����깺
const char PSI_STOCK_BC_OeFundPur = '8';
///����ʽ�������
const char PSI_STOCK_BC_OeFundRed = '9';
///����Ʒ����
const char PSI_STOCK_BC_CollateralIn = 'a';
///����Ʒ����
const char PSI_STOCK_BC_CollateralOut = 'b';
///��Ѻ���
const char PSI_STOCK_BC_PledgeIn = 'd';
///��Ѻ����
const char PSI_STOCK_BC_PledgeOut = 'e';
///�����ծ
const char PSI_STOCK_BC_Rationed = 'f';
///����ʽ������
const char PSI_STOCK_BC_Split = 'g';
///����ʽ����ϲ�
const char PSI_STOCK_BC_Merge = 'h';
///��������
const char PSI_STOCK_BC_CreditBuy = 'i';
///��ȯ����
const char PSI_STOCK_BC_CreditSell = 'j';
///��ȯ����
const char PSI_STOCK_BC_SellRepay = 'k';
///��ȯ��ȯ
const char PSI_STOCK_BC_BuyRepay = 'l';
///��ȯ��ת
const char PSI_STOCK_BC_RepayTransfer = 'm';
///��ȯ��ת
const char PSI_STOCK_BC_SurplusTransfer = 'n';
///Դȯ��ת
const char PSI_STOCK_BC_SourceTransfer = 'o';
///��ȯǿƽ����(�ڲ�ʹ��)
const char PSI_STOCK_BC_ForceSellRepay = 'p';
///��ȯǿƽ��ȯ(�ڲ�ʹ��)
const char PSI_STOCK_BC_ForceBuyRepay = 'q';
///��ծչ��(�ڲ�ʹ��)
const char PSI_STOCK_BC_DebtExtend = 'r';
///ת�й�
const char PSI_STOCK_BC_CustodyTransfer = 's';
///ծȯת��
const char PSI_STOCK_BC_BondConvertStock = 't';
///ծȯ����
const char PSI_STOCK_BC_BondPutback = 'u';
///ETFʵ���깺
const char PSI_STOCK_BC_ETFOtPur = 'v';
///ETFʵ�����
const char PSI_STOCK_BC_ETFOtRed = 'w';
///���۳���
const char PSI_STOCK_BC_PutbackRelieve = 'x';
typedef char PSIStockBizClassType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockBrokerageTypeType��һ��Ӷ����������
/////////////////////////////////////////////////////////////////////////
///ëӶ��
const char PSI_STOCK_BT_Gross = '0';
///��Ӷ��
const char PSI_STOCK_BT_Net = '1';
typedef char PSIStockBrokerageTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockLongVolumeType��һ��LongVolume����
/////////////////////////////////////////////////////////////////////////
#ifdef WINDOWS
typedef __int64 PSIStockLongVolumeType;
#else
typedef long long int PSIStockLongVolumeType;
#endif

/////////////////////////////////////////////////////////////////////////
/// PSIStockOperateSourceType��һ��������Դ����
/////////////////////////////////////////////////////////////////////////
///ʵʱ�ϳ�
const char PSI_STOCK_OPRTSRC_DBCommand = '0';
///API����
const char PSI_STOCK_OPRTSRC_SyncAPI = '1';
///�Զ�����
const char PSI_STOCK_OPRTSRC_AutoTrigger = '2';
///API�����ϳ�
const char PSI_STOCK_OPRTSRC_EmergencyAPI = '3';
typedef char PSIStockOperateSourceType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockExternalSerialType��һ���ⲿ��ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockExternalSerialType[65];

/////////////////////////////////////////////////////////////////////////
/// PSIStockBizRefType��һ��ҵ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockBizRefType[21];

/////////////////////////////////////////////////////////////////////////
/// PSIStockIPONumberIDType��һ���¹��깺��ɺ�����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockIPONumberIDType[31];

/////////////////////////////////////////////////////////////////////////
/// PSIStockSpecPrivilegeTypeType��һ������Ȩ���������
/////////////////////////////////////////////////////////////////////////
///��ҵ��
const char PSI_STOCK_SPLT_GEM = '0';
///���վ�ʾ��
const char PSI_STOCK_SPLT_RiskWarning = '1';
///����������
const char PSI_STOCK_SPLT_Delisting = '2';
///�۹�ͨ
const char PSI_STOCK_SPLT_SZSEHK = '3';
///�ƴ���
const char PSI_STOCK_SPLT_SHKC = '4';
///��ҵ��ע����
const char PSI_STOCK_SPLT_GEMRegistration = '5';
///�ּ�����
const char PSI_STOCK_SPLT_StructFund = '6';
///��תծ
const char PSI_STOCK_SPLT_ConvertBond = '7';
///������ʩ����
const char PSI_STOCK_SPLT_InfrastructureFund = '8';
///�����תծ
const char PSI_STOCK_SPLT_OrientedConvertBond = '9';
///��������Ʊ
const char PSI_STOCK_SPLT_BJStock = 'a';
///���彻��
const char PSI_STOCK_SPLT_Main = 'b';
///�����������תծ
const char PSI_STOCK_SPLT_DelConvertBond = 'c';
typedef char PSIStockSpecPrivilegeTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockCreRedTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��ͨ����
const char PSI_STOCK_CRT_IS = '0';
///ʵ������
const char PSI_STOCK_CRT_OS = '1';
typedef char PSIStockCreRedTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockETFCurrenceReplaceStatusType��һ��ETF�ֽ������־����
/////////////////////////////////////////////////////////////////////////
///��ֹ�ֽ����
const char PSI_STOCK_ETFCTSTAT_Forbidden = '0';
///�����ֽ����
const char PSI_STOCK_ETFCTSTAT_Allow = '1';
///�����ֽ����
const char PSI_STOCK_ETFCTSTAT_Force = '2';
///�����˲��ֽ����
const char PSI_STOCK_ETFCTSTAT_CBAllow = '3';
///���б����ֽ����
const char PSI_STOCK_ETFCTSTAT_CBForce = '4';
typedef char PSIStockETFCurrenceReplaceStatusType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockUUPICType��һ��ͨ��ͳһ����ʶ��������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockUUPICType[16];

/////////////////////////////////////////////////////////////////////////
/// PSIStockPriceTickIDType��һ���۲�Ʒ������
/////////////////////////////////////////////////////////////////////////
///�ɱ�֤ȯ
const char PSI_STOCK_PTID_Stock = '0';
///ծ��֤ȯ
const char PSI_STOCK_PTID_Bond = '1';
///��Ʊ��Ȩ
const char PSI_STOCK_PTID_Option = '2';
///��������
const char PSI_STOCK_PTID_Fund = '3';
typedef char PSIStockPriceTickIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPriceTickGroupIDType��һ���۲��������
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockPriceTickGroupIDType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockPriceTickTypeType��һ���۲��������
/////////////////////////////////////////////////////////////////////////
///�۸�������
const char PSI_STOCK_PPT_LimitPrice = '0';
///�۲���
const char PSI_STOCK_PPT_PriceTick = '1';
typedef char PSIStockPriceTickTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockFundTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��׼LOF
const char PSI_STOCK_FUT_Normal = '0';
///�ּ�ĸ����
const char PSI_STOCK_FUT_Mother = '1';
///�ּ��ӻ���
const char PSI_STOCK_FUT_Sub = '2';
typedef char PSIStockFundTypeType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockNodeInfoType��һ���ڵ���Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockNodeInfoType[33];

/////////////////////////////////////////////////////////////////////////
/// PSIStockFensVerType��һ���汾������
/////////////////////////////////////////////////////////////////////////
typedef short PSIStockFensVerType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockFensEnvIDType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockFensEnvIDType[13];

/////////////////////////////////////////////////////////////////////////
/// PSIStockFensNodeIDType��һ���ڵ�������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockFensNodeIDType[11];

/////////////////////////////////////////////////////////////////////////
/// PSIStockFensUserIDType��һ���û���������
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockFensUserIDType[16];

/////////////////////////////////////////////////////////////////////////
/// PSIStockClientInfoType��һ���ն���Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char PSIStockClientInfoType[65];

/////////////////////////////////////////////////////////////////////////
/// PSIStockPortType��һ���˿ں�����
/////////////////////////////////////////////////////////////////////////
typedef int PSIStockPortType;

/////////////////////////////////////////////////////////////////////////
/// PSIStockTradeCommModeType��һ������ͨѶģʽ����
/////////////////////////////////////////////////////////////////////////
///TCPģʽ
const char PSI_STOCK_TCM_TCP = '0';
///UDPģʽ
const char PSI_STOCK_TCM_UDP = '1';
///PROXYģʽ
const char PSI_STOCK_TCM_PROXY = '2';
///sf����tcpdirectģʽ-TCP
const char PSI_STOCK_TCM_TCPDIRECT = '3';
typedef char PSIStockTradeCommModeType;

/*
 *	���ݴ���ģ���¼�
 */
typedef enum queryEvent
{
    QUERY			= 0,		//�����¼�
    QUERY_BROKER_CREDIT,						//BrokerCredit
    QUERY_POSITION,						//��ѯ�ֲ�
    QUERY_ORDER,						//��ѯ����
    QUERY_TRADER,                        //��ѯ�ɽ�
    QUERY_ACCOUNT                       //��ѯ�ʽ�
}QueryEvent;

typedef long long int llint;
