/*!
 * \file PsiSubTrader.h
 * \project	PsiTraderMagicWeapon
 *
 * \author l<PERSON><PERSON>
 * \date 2024/6/25.
 *
 * \brief ����ģ�����߳�
 */
#pragma once

#include <boost/asio.hpp>
// 函数: Boost.Asio 版本兼容性处理
// 作用: Ubuntu 18.04的Boost版本较老，可能没有executor_work_guard.hpp
#if BOOST_VERSION >= 106600
#include <boost/asio/executor_work_guard.hpp>
#else
// 使用旧版本的 io_service::work 替代
#include <boost/asio/io_service.hpp>
#endif
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"
#include "PsiVariant.hpp"
#include "PsiBaseDataMgr.h"
#include "TraderCTP.h"
#include "PsiTraderDataMgr.h"


class PsiTraderTcpShmSpi;
class PsiSubTrader {
public:
    PsiSubTrader();
    ~PsiSubTrader();

    /**
     *  ��ʼ��
     * @param pBaseDataMgr
     * @param pDataMgr
     * @param traderId
     * @return
     */
    bool init(PsiBaseDataMgr* pBaseDataMgr, PsiDataMgr* pDataMgr, PsiTraderDataMgr* psiTraderDataMgr,
              PsiRedisDataMgr* psiRedisDataMgr, const char* traderId, const char* subTraderId, const char* traderModule,PsiTraderTcpShmSpi* pTraderTcpShmSpi);

    /**
     * ����
     * @return
     */
    bool run();

private:
    TraderCTP* m_pTraderCTP;

    PsiBaseDataMgr* mp_baseDataMgr; // �������ݹ���ģ��

    PsiDataMgr* mp_dataMgr; // ���ݹ���ģ��

    PsiTraderDataMgr* mp_tdMgr; // �������ݹ�����

    PsiRedisDataMgr* mp_rdMgr; // Redis���ݹ�����

    std::string m_traderId; // ����ID
    std::string m_subTraderId; // ����ID
    std::string m_traderModule; // ����ģ������

    StdThreadPtr m_worker;

    PsiTraderTcpShmSpi* m_pTraderTcpShmSpi; // ���׻ص��ӿ�
};



