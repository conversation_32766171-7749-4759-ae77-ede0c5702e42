/*!
 * \file PsiShareDataMgr.cpp
 * \project	PsiRedisDataMgr
 *
* \author liji<PERSON>
* \date 2024/03/07
 *
 * \brief 存储所有票共享数据
 */
#pragma once
#include <string>
#include "lockfree.hpp"
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"


class PsiStraShareDataMgr {
public:
    PsiStraShareDataMgr();
    ~PsiStraShareDataMgr();

public:
    std::atomic<int> m_conditionsStdCount; // 满足条件的股票数量

    std::atomic<int> m_buyStdCount; // 购买的股票数量
};
