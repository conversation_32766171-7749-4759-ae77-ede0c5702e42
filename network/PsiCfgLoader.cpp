#include "PsiCfgLoader.h"
#include "StrUtil.hpp"
#include "StdUtils.hpp"

#include "charconv.hpp"

#include "PsiVariant.hpp"
#include <rapidjson/document.h>
#include <rapidjson/writer.h>
#include <rapidjson/stringbuffer.h>
namespace rj = rapidjson;


bool json_to_variant(const rj::Value& root, PsiVariant* params)
{
	if (root.IsObject() && params->type() != PsiVariant::VT_Object)
		return false;

	if (root.IsArray() && params->type() != PsiVariant::VT_Array)
		return false;

	if (root.IsObject())
	{
        for (auto it = root.MemberBegin(); it != root.MemberEnd(); ++it)
        {
            auto& m = *it;
			const char* key = m.name.GetString();
			const rj::Value& item = m.value;
			switch (item.GetType())
			{
			case rj::kObjectType:
			{
                PsiVariant* subObj = PsiVariant::createObject();
				if (json_to_variant(item, subObj))
					params->append(key, subObj, false);
			}
			break;
			case rj::kArrayType:
			{
                PsiVariant* subAy = PsiVariant::createArray();
				if (json_to_variant(item, subAy))
					params->append(key, subAy, false);
			}
			break;
			case rj::kNumberType:
				if (item.IsInt())
					params->append(key, item.GetInt());
				else if (item.IsUint())
					params->append(key, item.GetUint());
				else if (item.IsInt64())
					params->append(key, item.GetInt64());
				else if (item.IsUint64())
					params->append(key, item.GetUint64());
				else if (item.IsDouble())
					params->append(key, item.GetDouble());
				break;
			case rj::kStringType:
				params->append(key, item.GetString());
				break;
			case rj::kTrueType:
			case rj::kFalseType:
				params->append(key, item.GetBool());
				break;

			}
		}
	}
	else
	{
		for (auto& item : root.GetArray())
		{
			switch (item.GetType())
			{
			case rj::kObjectType:
			{
                PsiVariant* subObj = PsiVariant::createObject();
				if (json_to_variant(item, subObj))
					params->append(subObj, false);
			}
			break;
			case rj::kArrayType:
			{
                PsiVariant* subAy = PsiVariant::createArray();
				if (json_to_variant(item, subAy))
					params->append(subAy, false);
			}
			break;
			case rj::kNumberType:
				if (item.IsInt())
					params->append(item.GetInt());
				else if (item.IsUint())
					params->append(item.GetUint());
				else if (item.IsInt64())
					params->append(item.GetInt64());
				else if (item.IsUint64())
					params->append(item.GetUint64());
				else if (item.IsDouble())
					params->append(item.GetDouble());
				break;
			case rj::kStringType:
				params->append(item.GetString());
				break;
			case rj::kTrueType:
			case rj::kFalseType:
				params->append(item.GetBool());
				break;
			}
		}
	}
	return true;
}

PsiVariant* PsiCfgLoader::load_from_json(const char* content)
{
	rj::Document root;
	root.Parse(content);

	if (root.HasParseError())
		return NULL;

    PsiVariant* ret = PsiVariant::createObject();
	if (!json_to_variant(root, ret))
	{
		ret->release();
		return NULL;
	}

	return ret;
}

std::string PsiCfgLoader::structToJson(const PSIDoTraderOrderStruct& person) {
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("stdCode", rapidjson::Value(person.code, allocator).Move(), allocator);
    document.AddMember("exch", rapidjson::Value(person.exch, allocator).Move(), allocator);
    document.AddMember("straUuid", rapidjson::Value(person.stra_uuid, allocator).Move(), allocator);
    std::string side(1, person.direction);
    document.AddMember("side", rapidjson::Value(side.c_str(), allocator), allocator);
    std::string type(1, person.type);
    document.AddMember("type", rapidjson::Value(type.c_str(), allocator), allocator);
    std::string orderStatus(1, person.order_status);
    document.AddMember("orderStatus", rapidjson::Value(orderStatus.c_str(), allocator), allocator);
    std::string orderType(1, person.order_type);
    document.AddMember("orderType", rapidjson::Value(orderType.c_str(), allocator), allocator);
    document.AddMember("orderSysId", rapidjson::Value(person.order_sys_id, allocator).Move(), allocator);
    document.AddMember("msg", rapidjson::Value(ChartoUTF8(person.msg), allocator).Move(), allocator);
    document.AddMember("accountId", rapidjson::Value(person.account_id, allocator).Move(), allocator);
    document.AddMember("insertDate", rapidjson::Value(person.insert_date, allocator).Move(), allocator);
    document.AddMember("insertTime", rapidjson::Value(person.insert_time, allocator).Move(), allocator);
    document.AddMember("acceptTime", rapidjson::Value(person.accept_time, allocator).Move(), allocator);
    document.AddMember("cancelTime", rapidjson::Value(person.cancel_time, allocator).Move(), allocator);
    document.AddMember("volume", rapidjson::Value(person.volume), allocator);
    document.AddMember("tradeVolume", rapidjson::Value(person.trade_volume), allocator);
    document.AddMember("cancelVolume", rapidjson::Value(person.cancel_volume), allocator);
    document.AddMember("tradeAmount", rapidjson::Value(person.trade_amount), allocator);
    document.AddMember("triggerOrderId", rapidjson::Value(person.order_no), allocator);
    document.AddMember("nSeals", rapidjson::Value(person.seal_board), allocator);
    document.AddMember("price", rapidjson::Value(person.price), allocator);
    document.AddMember("orderRef", rapidjson::Value(person.order_ref), allocator);
    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

/**
 * ���ֲ���Ϣת����JSON
 * @param positionField
 * @param accountDate
 * @param traderId
 * @param accountType
 * @param status
 * @param msg
 * @return
 */
std::string PsiCfgLoader::positionStructToJson(TORASTOCKAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId){
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("account_id", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("account_date", rapidjson::Value(accountDate), allocator);
    std::string exch(1, positionField.ExchangeID);
    document.AddMember("exch", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("stock_code", rapidjson::Value(positionField.SecurityID, allocator), allocator);
    document.AddMember("volume", rapidjson::Value(positionField.CurrentPosition), allocator);
    document.AddMember("can_use_volume", rapidjson::Value(positionField.AvailablePosition), allocator);
    document.AddMember("market_value", rapidjson::Value(positionField.TotalPosCost), allocator);
    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

/**
 * ���ֲ���Ϣת����JSON
 * @param positionField
 * @param accountDate
 * @param traderId
 * @param accountType
 * @param status
 * @param msg
 * @return
 */
std::string PsiCfgLoader::creditPositionStructToJson(TORACREDITAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId){
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("account_id", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("account_date", rapidjson::Value(accountDate), allocator);
    std::string exch(1, positionField.ExchangeID);
    document.AddMember("exch", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("stock_code", rapidjson::Value(positionField.SecurityID, allocator), allocator);
    document.AddMember("volume", rapidjson::Value(positionField.CurrentPosition), allocator);
    document.AddMember("can_use_volume", rapidjson::Value(positionField.AvailablePosition), allocator);
    document.AddMember("market_value", rapidjson::Value(positionField.TotalPosCost), allocator);
    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

///�¼ӵ� �ֲ�ת��Ϊjson������ת��Ϊjson���ɽ�ת��Ϊjson
std::string PsiCfgLoader::positionStructToJsonX(TORASTOCKAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
/*    document.AddMember("account_id", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("account_date", rapidjson::Value(accountDate), allocator);*/
    std::string exch(1, positionField.ExchangeID);
    document.AddMember("AccountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("AccountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("ExchangeID", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("InvestorID", rapidjson::Value(positionField.InvestorID, allocator).Move(), allocator);
    document.AddMember("BusinessUnitID", rapidjson::Value(positionField.BusinessUnitID, allocator).Move(), allocator);
    std::string marketid(1, positionField.MarketID);
    document.AddMember("MarketID", rapidjson::Value(marketid.c_str(), allocator), allocator);
    document.AddMember("ShareholderID", rapidjson::Value(positionField.ShareholderID, allocator).Move(), allocator);
    document.AddMember("TradingDay", rapidjson::Value(positionField.TradingDay, allocator).Move(), allocator);
    document.AddMember("SecurityID", rapidjson::Value(positionField.SecurityID, allocator), allocator);
    document.AddMember("SecurityName", rapidjson::Value(ChartoUTF8(positionField.SecurityName), allocator).Move(), allocator);
    document.AddMember("HistoryPos", rapidjson::Value(positionField.HistoryPos), allocator);
    document.AddMember("HistoryPosFrozen", rapidjson::Value(positionField.HistoryPosFrozen), allocator);
    document.AddMember("TodayBSPos", rapidjson::Value(positionField.TodayBSPos), allocator);
    document.AddMember("TodayBSPosFrozen", rapidjson::Value(positionField.TodayBSPosFrozen), allocator);
    document.AddMember("TodayPRPosFrozen", rapidjson::Value(positionField.TodayPRPosFrozen), allocator);
    document.AddMember("TodaySMPos", rapidjson::Value(positionField.TodaySMPos), allocator);
    document.AddMember("TodaySMPosFrozen", rapidjson::Value(positionField.TodaySMPosFrozen), allocator);
    document.AddMember("HistoryPosPrice", rapidjson::Value(positionField.HistoryPosPrice), allocator);
    document.AddMember("TotalPosCost", rapidjson::Value(positionField.TotalPosCost), allocator);
    document.AddMember("PrePosition", rapidjson::Value(positionField.PrePosition), allocator);
    document.AddMember("AvailablePosition", rapidjson::Value(positionField.AvailablePosition), allocator);
    document.AddMember("CurrentPosition", rapidjson::Value(positionField.CurrentPosition), allocator);
    document.AddMember("OpenPosCost",rapidjson::Value(positionField.OpenPosCost), allocator);
    document.AddMember("CreditBuyPos",rapidjson::Value(positionField.CreditBuyPos), allocator);
    document.AddMember("CreditSellPos",rapidjson::Value(positionField.CreditSellPos), allocator);
    document.AddMember("TodayCreditSellPos",rapidjson::Value(positionField.TodayCreditSellPos), allocator);
    document.AddMember("CollateralOutPos",rapidjson::Value(positionField.CollateralOutPos), allocator);
    document.AddMember("RepayUntradeVolume",rapidjson::Value(positionField.RepayUntradeVolume), allocator);
    document.AddMember("RepayTransferUntradeVolume",rapidjson::Value(positionField.RepayTransferUntradeVolume), allocator);
    document.AddMember("CollateralBuyUntradeAmount",rapidjson::Value(positionField.CollateralBuyUntradeAmount), allocator);
    document.AddMember("CollateralBuyUntradeVolume",rapidjson::Value(positionField.CollateralBuyUntradeVolume), allocator);
    document.AddMember("CreditBuyAmount",rapidjson::Value(positionField.CreditBuyAmount), allocator);
    document.AddMember("CreditBuyUntradeAmount",rapidjson::Value(positionField.CreditBuyUntradeAmount), allocator);
    document.AddMember("CreditBuyFrozenMargin",rapidjson::Value(positionField.CreditBuyFrozenMargin), allocator);
    document.AddMember("CreditBuyInterestFee",rapidjson::Value(positionField.CreditBuyInterestFee), allocator);
    document.AddMember("CreditBuyUntradeVolume",rapidjson::Value(positionField.CreditBuyUntradeVolume), allocator);
    document.AddMember("CreditSellAmount",rapidjson::Value(positionField.CreditSellAmount), allocator);
    document.AddMember("CreditSellUntradeAmount",rapidjson::Value(positionField.CreditSellUntradeAmount), allocator);
    document.AddMember("CreditSellFrozenMargin",rapidjson::Value(positionField.CreditSellFrozenMargin), allocator);
    document.AddMember("CreditSellInterestFee",rapidjson::Value(positionField.CreditSellInterestFee), allocator);
    document.AddMember("CreditSellUntradeVolume",rapidjson::Value(positionField.CreditSellUntradeVolume), allocator);
    document.AddMember("CollateralInPos",rapidjson::Value(positionField.CollateralInPos), allocator);
    document.AddMember("CreditBuyFrozenCirculateMargin",rapidjson::Value(positionField.CreditBuyFrozenCirculateMargin), allocator);
    document.AddMember("CreditSellFrozenCirculateMargin",rapidjson::Value(positionField.CreditSellFrozenCirculateMargin), allocator);
    document.AddMember("CloseProfit",rapidjson::Value(positionField.CloseProfit), allocator);
    document.AddMember("TodayTotalOpenVolume",rapidjson::Value(positionField.TodayTotalOpenVolume), allocator);
    document.AddMember("TodayCommission",rapidjson::Value(positionField.TodayCommission), allocator);
    document.AddMember("TodayTotalBuyAmount",rapidjson::Value(positionField.TodayTotalBuyAmount), allocator);
    document.AddMember("TodayTotalSellAmount",rapidjson::Value(positionField.TodayTotalSellAmount), allocator);
    document.AddMember("PreFrozen",rapidjson::Value(positionField.PreFrozen), allocator);

    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

std::string PsiCfgLoader::tradeStructToJson(TORASTOCKAPI::CTORATstpTradeField pTradeField, uint32_t accountDate, const char* traderId)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();

    std::string exch(1, pTradeField.ExchangeID);
    document.AddMember("AccountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("AccountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("ExchangeID", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("DepartmentID", rapidjson::Value(pTradeField.DepartmentID, allocator).Move(), allocator);
    document.AddMember("InvestorID", rapidjson::Value(pTradeField.InvestorID, allocator).Move(), allocator);
    document.AddMember("BusinessUnitID", rapidjson::Value(pTradeField.BusinessUnitID, allocator).Move(), allocator);
    document.AddMember("ShareholderID", rapidjson::Value(pTradeField.ShareholderID, allocator).Move(), allocator);
    document.AddMember("SecurityID", rapidjson::Value(pTradeField.SecurityID, allocator), allocator);
    document.AddMember("TradeID", rapidjson::Value(pTradeField.TradeID, allocator).Move(), allocator);
    std::string Direction(1, pTradeField.Direction);
    document.AddMember("Direction", rapidjson::Value(Direction.c_str(), allocator), allocator);

    document.AddMember("OrderSysID", rapidjson::Value(pTradeField.OrderSysID, allocator), allocator);
    document.AddMember("OrderLocalID", rapidjson::Value(pTradeField.OrderLocalID, allocator).Move(), allocator);
    document.AddMember("Price", rapidjson::Value(pTradeField.Price), allocator);
    document.AddMember("Volume", rapidjson::Value(pTradeField.Volume), allocator);
    document.AddMember("TradeDate", rapidjson::Value(pTradeField.TradeDate, allocator), allocator);
    document.AddMember("TradeTime", rapidjson::Value(pTradeField.TradeTime, allocator), allocator);
    document.AddMember("TradingDay", rapidjson::Value(pTradeField.TradingDay, allocator), allocator);
    document.AddMember("PbuID", rapidjson::Value(pTradeField.PbuID, allocator), allocator);
    document.AddMember("OrderRef", rapidjson::Value(pTradeField.OrderRef), allocator);
    document.AddMember("AccountID", rapidjson::Value(pTradeField.AccountID, allocator), allocator);
    std::string CurrencyID(1, pTradeField.CurrencyID);
    document.AddMember("CurrencyID", rapidjson::Value(CurrencyID.c_str(), allocator), allocator);

    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

std::string PsiCfgLoader::orderStructToJson(TORASTOCKAPI::CTORATstpOrderField pOrderField, uint32_t accountDate, const char* traderId)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    std::string exch(1, pOrderField.ExchangeID);
    document.AddMember("AccountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("AccountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("ExchangeID", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("InvestorID", rapidjson::Value(pOrderField.InvestorID, allocator).Move(), allocator);
    document.AddMember("BusinessUnitID", rapidjson::Value(pOrderField.BusinessUnitID, allocator).Move(), allocator);
    document.AddMember("ShareholderID", rapidjson::Value(pOrderField.ShareholderID, allocator).Move(), allocator);
    document.AddMember("SecurityID", rapidjson::Value(pOrderField.SecurityID, allocator), allocator);
    std::string Direction(1, pOrderField.Direction);
    document.AddMember("Direction", rapidjson::Value(Direction.c_str(), allocator), allocator);
    std::string OrderPriceType(1, pOrderField.OrderPriceType);
    document.AddMember("OrderPriceType", rapidjson::Value(OrderPriceType.c_str(), allocator), allocator);
    std::string TimeCondition(1, pOrderField.TimeCondition);
    document.AddMember("TimeCondition", rapidjson::Value(TimeCondition.c_str(), allocator), allocator);
    std::string VolumeCondition(1, pOrderField.VolumeCondition);
    document.AddMember("VolumeCondition", rapidjson::Value(VolumeCondition.c_str(), allocator), allocator);
    document.AddMember("LimitPrice", rapidjson::Value(pOrderField.LimitPrice), allocator);
    document.AddMember("VolumeTotalOriginal", rapidjson::Value(pOrderField.VolumeTotalOriginal), allocator);
    std::string LotType(1, pOrderField.LotType);
    document.AddMember("LotType", rapidjson::Value(LotType.c_str(), allocator), allocator);
    document.AddMember("GTDate", rapidjson::Value(pOrderField.GTDate, allocator).Move(), allocator);
    std::string Operway(1, pOrderField.Operway);
    document.AddMember("Operway", rapidjson::Value(Operway.c_str(), allocator), allocator);
    std::string CondCheck(1, pOrderField.CondCheck);
    document.AddMember("CondCheck", rapidjson::Value(CondCheck.c_str(), allocator), allocator);
    document.AddMember("SInfo", rapidjson::Value(pOrderField.SInfo, allocator).Move(), allocator);
    document.AddMember("IInfo", rapidjson::Value(pOrderField.IInfo), allocator);
    document.AddMember("RequestID", rapidjson::Value(pOrderField.RequestID), allocator);
    document.AddMember("FrontID", rapidjson::Value(pOrderField.FrontID), allocator);
    document.AddMember("SessionID", rapidjson::Value(pOrderField.SessionID), allocator);
    document.AddMember("OrderRef", rapidjson::Value(pOrderField.OrderRef), allocator);
    document.AddMember("OrderLocalID", rapidjson::Value(pOrderField.OrderLocalID, allocator).Move(), allocator);
    document.AddMember("OrderSysID", rapidjson::Value(pOrderField.OrderSysID, allocator).Move(), allocator);
    std::string OrderStatus(1, pOrderField.OrderStatus);
    document.AddMember("OrderStatus", rapidjson::Value(OrderStatus.c_str(), allocator), allocator);
    std::string OrderSubmitStatus(1, pOrderField.OrderSubmitStatus);
    document.AddMember("OrderSubmitStatus", rapidjson::Value(OrderSubmitStatus.c_str(), allocator), allocator);
    document.AddMember("StatusMsg", rapidjson::Value(ChartoUTF8(pOrderField.StatusMsg), allocator).Move(), allocator);
    document.AddMember("VolumeTraded", rapidjson::Value(pOrderField.VolumeTraded), allocator);
    document.AddMember("VolumeCanceled", rapidjson::Value(pOrderField.VolumeCanceled), allocator);
    document.AddMember("TradingDay", rapidjson::Value(pOrderField.TradingDay, allocator).Move(), allocator);
    document.AddMember("InsertUser", rapidjson::Value(pOrderField.InsertUser, allocator).Move(), allocator);
    document.AddMember("InsertDate", rapidjson::Value(pOrderField.InsertDate, allocator).Move(), allocator);
    document.AddMember("InsertTime", rapidjson::Value(pOrderField.InsertTime, allocator).Move(), allocator);
    document.AddMember("AcceptTime", rapidjson::Value(pOrderField.AcceptTime, allocator).Move(), allocator);
    document.AddMember("CancelUser", rapidjson::Value(pOrderField.CancelUser, allocator).Move(), allocator);
    document.AddMember("CancelTime", rapidjson::Value(pOrderField.CancelTime, allocator).Move(), allocator);
    document.AddMember("DepartmentID", rapidjson::Value(pOrderField.DepartmentID, allocator).Move(), allocator);
    document.AddMember("AccountID", rapidjson::Value(pOrderField.AccountID, allocator).Move(), allocator);
    std::string CurrencyID(1, pOrderField.CurrencyID);
    document.AddMember("CurrencyID", rapidjson::Value(CurrencyID.c_str(), allocator), allocator);
    document.AddMember("PbuID", rapidjson::Value(pOrderField.PbuID, allocator).Move(), allocator);
    document.AddMember("Turnover", rapidjson::Value(pOrderField.Turnover), allocator);
    std::string OrderType(1, pOrderField.OrderType);
    document.AddMember("OrderType", rapidjson::Value(OrderType.c_str(), allocator), allocator);
    document.AddMember("UserProductInfo", rapidjson::Value(pOrderField.UserProductInfo, allocator).Move(), allocator);
    std::string ForceCloseReason(1, pOrderField.ForceCloseReason);
    document.AddMember("ForceCloseReason", rapidjson::Value(ForceCloseReason.c_str(), allocator), allocator);
    document.AddMember("CreditQuotaID", rapidjson::Value(pOrderField.CreditQuotaID, allocator).Move(), allocator);
    std::string CreditQuotaType(1, pOrderField.CreditQuotaType);
    document.AddMember("CreditQuotaType", rapidjson::Value(CreditQuotaType.c_str(), allocator), allocator);
    document.AddMember("CreditDebtID", rapidjson::Value(pOrderField.CreditDebtID, allocator).Move(), allocator);
    document.AddMember("IPAddress", rapidjson::Value(pOrderField.IPAddress, allocator).Move(), allocator);
    document.AddMember("MacAddress", rapidjson::Value(pOrderField.MacAddress, allocator).Move(), allocator);
    document.AddMember("RtnFloatInfo", rapidjson::Value(pOrderField.RtnFloatInfo), allocator);
    document.AddMember("RtnIntInfo", rapidjson::Value(pOrderField.RtnIntInfo), allocator);
    document.AddMember("RtnFloatInfo1",rapidjson::Value(pOrderField.RtnFloatInfo1), allocator);
    document.AddMember("RtnFloatInfo2",rapidjson::Value(pOrderField.RtnFloatInfo2), allocator);
    document.AddMember("RtnFloatInfo3",rapidjson::Value(pOrderField.RtnFloatInfo3), allocator);

    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

/**
* ���ֲ���Ϣת����JSON Credit
* @param positionField
* @param accountDate
* @param traderId
* @return
*/
std::string PsiCfgLoader::CreditPositionStructToJson(TORACREDITAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("AccountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("AccountDate", rapidjson::Value(accountDate), allocator);
    std::string exch(1, positionField.ExchangeID);
    document.AddMember("ExchangeID", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("InvestorID", rapidjson::Value(positionField.InvestorID, allocator).Move(), allocator);
    document.AddMember("BusinessUnitID", rapidjson::Value(positionField.BusinessUnitID, allocator).Move(), allocator);
    std::string marketid(1, positionField.MarketID);
    document.AddMember("MarketID", rapidjson::Value(marketid.c_str(), allocator), allocator);
    document.AddMember("ShareholderID", rapidjson::Value(positionField.ShareholderID, allocator).Move(), allocator);
    document.AddMember("TradingDay", rapidjson::Value(positionField.TradingDay, allocator).Move(), allocator);
    document.AddMember("SecurityID", rapidjson::Value(positionField.SecurityID, allocator), allocator);
    document.AddMember("SecurityName", rapidjson::Value(ChartoUTF8(positionField.SecurityName), allocator).Move(), allocator);
    document.AddMember("HistoryPos", rapidjson::Value(positionField.HistoryPos), allocator);
    document.AddMember("HistoryPosFrozen", rapidjson::Value(positionField.HistoryPosFrozen), allocator);
    document.AddMember("TodayBSPos", rapidjson::Value(positionField.TodayBSPos), allocator);
    document.AddMember("TodayBSPosFrozen", rapidjson::Value(positionField.TodayBSPosFrozen), allocator);
    document.AddMember("TodayPRPos", rapidjson::Value(positionField.TodayPRPos), allocator);
    document.AddMember("TodayPRPosFrozen", rapidjson::Value(positionField.TodayPRPosFrozen), allocator);
    document.AddMember("TodaySMPos", rapidjson::Value(positionField.TodaySMPos), allocator);
    document.AddMember("TodaySMPosFrozen", rapidjson::Value(positionField.TodaySMPosFrozen), allocator);
    document.AddMember("HistoryPosPrice", rapidjson::Value(positionField.HistoryPosPrice), allocator);
    document.AddMember("TotalPosCost", rapidjson::Value(positionField.TotalPosCost), allocator);
    document.AddMember("PrePosition", rapidjson::Value(positionField.PrePosition), allocator);
    document.AddMember("AvailablePosition", rapidjson::Value(positionField.AvailablePosition), allocator);
    document.AddMember("CurrentPosition", rapidjson::Value(positionField.CurrentPosition), allocator);
    document.AddMember("OpenPosCost",rapidjson::Value(positionField.OpenPosCost), allocator);
    document.AddMember("CreditBuyPos",rapidjson::Value(positionField.CreditBuyPos), allocator);
    document.AddMember("CreditSellPos",rapidjson::Value(positionField.CreditSellPos), allocator);
    document.AddMember("TodayCreditSellPos",rapidjson::Value(positionField.TodayCreditSellPos), allocator);
    document.AddMember("CollateralOutPos",rapidjson::Value(positionField.CollateralOutPos), allocator);
    document.AddMember("RepayUntradeVolume",rapidjson::Value(positionField.RepayUntradeVolume), allocator);
    document.AddMember("RepayTransferUntradeVolume",rapidjson::Value(positionField.RepayTransferUntradeVolume), allocator);
    document.AddMember("CollateralBuyUntradeAmount",rapidjson::Value(positionField.CollateralBuyUntradeAmount), allocator);
    document.AddMember("CollateralBuyUntradeVolume",rapidjson::Value(positionField.CollateralBuyUntradeVolume), allocator);
    document.AddMember("CreditBuyAmount",rapidjson::Value(positionField.CreditBuyAmount), allocator);
    document.AddMember("CreditBuyUntradeAmount",rapidjson::Value(positionField.CreditBuyUntradeAmount), allocator);
    document.AddMember("CreditBuyFrozenMargin",rapidjson::Value(positionField.CreditBuyFrozenMargin), allocator);
    document.AddMember("CreditBuyInterestFee",rapidjson::Value(positionField.CreditBuyInterestFee), allocator);
    document.AddMember("CreditBuyUntradeVolume",rapidjson::Value(positionField.CreditBuyUntradeVolume), allocator);
    document.AddMember("CreditSellAmount",rapidjson::Value(positionField.CreditSellAmount), allocator);
    document.AddMember("CreditSellUntradeAmount",rapidjson::Value(positionField.CreditSellUntradeAmount), allocator);
    document.AddMember("CreditSellFrozenMargin",rapidjson::Value(positionField.CreditSellFrozenMargin), allocator);
    document.AddMember("CreditSellInterestFee",rapidjson::Value(positionField.CreditSellInterestFee), allocator);
    document.AddMember("CreditSellUntradeVolume",rapidjson::Value(positionField.CreditSellUntradeVolume), allocator);
    document.AddMember("CollateralInPos",rapidjson::Value(positionField.CollateralInPos), allocator);
    document.AddMember("CreditBuyFrozenCirculateMargin",rapidjson::Value(positionField.CreditBuyFrozenCirculateMargin), allocator);
    document.AddMember("CreditSellFrozenCirculateMargin",rapidjson::Value(positionField.CreditSellFrozenCirculateMargin), allocator);
    document.AddMember("CloseProfit",rapidjson::Value(positionField.CloseProfit), allocator);
    document.AddMember("TodayTotalOpenVolume",rapidjson::Value(positionField.TodayTotalOpenVolume), allocator);
    document.AddMember("RationVolume",rapidjson::Value(positionField.RationVolume), allocator);
    document.AddMember("RationAmount",rapidjson::Value(positionField.RationAmount), allocator);
    document.AddMember("TodayTotalBuyAmount",rapidjson::Value(positionField.TodayTotalBuyAmount), allocator);
    document.AddMember("TodayTotalSellAmount",rapidjson::Value(positionField.TodayTotalSellAmount), allocator);


    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();

}

/**
 * ���ɽ���Ϣת����JSON Credit
 * @param pTradeField
 * @param accountDate
 * @param traderId
 * @return
 */
std::string PsiCfgLoader::CreditTradeStructToJson(TORACREDITAPI::CTORATstpTradeField pTradeField, uint32_t accountDate, const char* traderId)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();

    std::string exch(1, pTradeField.ExchangeID);
    document.AddMember("AccountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("AccountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("ExchangeID", rapidjson::Value(exch.c_str(), allocator).Move(), allocator);
    document.AddMember("DepartmentID", rapidjson::Value(pTradeField.DepartmentID, allocator).Move(), allocator);
    document.AddMember("InvestorID", rapidjson::Value(pTradeField.InvestorID, allocator).Move(), allocator);
    document.AddMember("BusinessUnitID", rapidjson::Value(pTradeField.BusinessUnitID, allocator).Move(), allocator);
    document.AddMember("ShareholderID", rapidjson::Value(pTradeField.ShareholderID, allocator).Move(), allocator);
    document.AddMember("SecurityID", rapidjson::Value(pTradeField.SecurityID, allocator).Move(), allocator);
    document.AddMember("TradeID", rapidjson::Value(pTradeField.TradeID, allocator).Move(), allocator);
    std::string Direction(1, pTradeField.Direction);
    document.AddMember("Direction", rapidjson::Value(Direction.c_str(), allocator).Move(), allocator);
    document.AddMember("OrderSysID", rapidjson::Value(pTradeField.OrderSysID, allocator).Move(), allocator);
    document.AddMember("OrderLocalID", rapidjson::Value(pTradeField.OrderLocalID, allocator).Move(), allocator);
    document.AddMember("Price", rapidjson::Value(pTradeField.Price), allocator);
    document.AddMember("Volume", rapidjson::Value(pTradeField.Volume), allocator);
    document.AddMember("TradeDate", rapidjson::Value(pTradeField.TradeDate, allocator).Move(), allocator);
    document.AddMember("TradeTime", rapidjson::Value(pTradeField.TradeTime, allocator).Move(), allocator);
    document.AddMember("TradingDay", rapidjson::Value(pTradeField.TradingDay, allocator).Move(), allocator);
    document.AddMember("PbuID", rapidjson::Value(pTradeField.PbuID, allocator).Move(), allocator);
    document.AddMember("OrderRef", rapidjson::Value(pTradeField.OrderRef), allocator);
    document.AddMember("AccountId", rapidjson::Value(pTradeField.AccountID, allocator).Move(), allocator);
    std::string CurrencyID(1, pTradeField.CurrencyID);
    document.AddMember("CurrencyID", rapidjson::Value(CurrencyID.c_str(), allocator).Move(), allocator);


    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}


/**
 * ��������Ϣת����JSON  Credit
 * @param pTradeField
 * @param accountDate
 * @param traderId
 * @return
 */
std::string PsiCfgLoader::CreditOrderStructToJson(TORACREDITAPI::CTORATstpOrderField pOrderField, uint32_t accountDate, const char* traderId)
{
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    std::string exch(1, pOrderField.ExchangeID);
    document.AddMember("AccountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("AccountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("ExchangeID", rapidjson::Value(exch.c_str(), allocator), allocator);
    document.AddMember("InvestorID", rapidjson::Value(pOrderField.InvestorID, allocator).Move(), allocator);
    document.AddMember("BusinessUnitID", rapidjson::Value(pOrderField.BusinessUnitID, allocator).Move(), allocator);
    document.AddMember("ShareholderID", rapidjson::Value(pOrderField.ShareholderID, allocator).Move(), allocator);
    document.AddMember("SecurityID", rapidjson::Value(pOrderField.SecurityID, allocator), allocator);
    std::string Direction(1, pOrderField.Direction);
    document.AddMember("Direction", rapidjson::Value(Direction.c_str(), allocator), allocator);
    std::string OrderPriceType(1, pOrderField.OrderPriceType);
    document.AddMember("OrderPriceType", rapidjson::Value(OrderPriceType.c_str(), allocator), allocator);
    std::string TimeCondition(1, pOrderField.TimeCondition);
    document.AddMember("TimeCondition", rapidjson::Value(TimeCondition.c_str(), allocator), allocator);
    std::string VolumeCondition(1, pOrderField.VolumeCondition);
    document.AddMember("VolumeCondition", rapidjson::Value(VolumeCondition.c_str(), allocator), allocator);
    document.AddMember("LimitPrice", rapidjson::Value(pOrderField.LimitPrice), allocator);
    document.AddMember("VolumeTotalOriginal", rapidjson::Value(pOrderField.VolumeTotalOriginal), allocator);
    std::string LotType(1, pOrderField.LotType);
    document.AddMember("LotType", rapidjson::Value(LotType.c_str(), allocator), allocator);
    document.AddMember("GTDate", rapidjson::Value(pOrderField.GTDate, allocator).Move(), allocator);
    std::string Operway(1, pOrderField.Operway);
    document.AddMember("Operway", rapidjson::Value(Operway.c_str(), allocator), allocator);
    std::string CondCheck(1, pOrderField.CondCheck);
    document.AddMember("CondCheck", rapidjson::Value(CondCheck.c_str(), allocator), allocator);
    document.AddMember("SInfo", rapidjson::Value(pOrderField.SInfo, allocator).Move(), allocator);
    document.AddMember("IInfo", rapidjson::Value(pOrderField.IInfo), allocator);
    document.AddMember("RequestID", rapidjson::Value(pOrderField.RequestID), allocator);
    document.AddMember("FrontID", rapidjson::Value(pOrderField.FrontID), allocator);
    document.AddMember("SessionID", rapidjson::Value(pOrderField.SessionID), allocator);
    document.AddMember("OrderRef", rapidjson::Value(pOrderField.OrderRef), allocator);
    document.AddMember("OrderLocalID", rapidjson::Value(pOrderField.OrderLocalID, allocator).Move(), allocator);
    document.AddMember("OrderSysID", rapidjson::Value(pOrderField.OrderSysID, allocator).Move(), allocator);
    std::string OrderStatus(1, pOrderField.OrderStatus);
    document.AddMember("OrderStatus", rapidjson::Value(OrderStatus.c_str(), allocator), allocator);
    std::string OrderSubmitStatus(1, pOrderField.OrderSubmitStatus);
    document.AddMember("OrderSubmitStatus", rapidjson::Value(OrderSubmitStatus.c_str(), allocator), allocator);
    document.AddMember("StatusMsg", rapidjson::Value(ChartoUTF8(pOrderField.StatusMsg), allocator).Move(), allocator);
    document.AddMember("VolumeTraded", rapidjson::Value(pOrderField.VolumeTraded), allocator);
    document.AddMember("VolumeCanceled", rapidjson::Value(pOrderField.VolumeCanceled), allocator);
    document.AddMember("TradingDay", rapidjson::Value(pOrderField.TradingDay, allocator).Move(), allocator);
    document.AddMember("InsertUser", rapidjson::Value(pOrderField.InsertUser, allocator).Move(), allocator);
    document.AddMember("InsertDate", rapidjson::Value(pOrderField.InsertDate, allocator).Move(), allocator);
    document.AddMember("InsertTime", rapidjson::Value(pOrderField.InsertTime, allocator).Move(), allocator);
    document.AddMember("AcceptTime", rapidjson::Value(pOrderField.AcceptTime, allocator).Move(), allocator);
    document.AddMember("CancelUser", rapidjson::Value(pOrderField.CancelUser, allocator).Move(), allocator);
    document.AddMember("CancelTime", rapidjson::Value(pOrderField.CancelTime, allocator).Move(), allocator);
    document.AddMember("DepartmentID", rapidjson::Value(pOrderField.DepartmentID, allocator).Move(), allocator);
    document.AddMember("AccountID", rapidjson::Value(pOrderField.AccountID, allocator).Move(), allocator);
    std::string CurrencyID(1, pOrderField.CurrencyID);
    document.AddMember("CurrencyID", rapidjson::Value(CurrencyID.c_str(), allocator), allocator);
    document.AddMember("PbuID", rapidjson::Value(pOrderField.PbuID, allocator).Move(), allocator);
    document.AddMember("Turnover", rapidjson::Value(pOrderField.Turnover), allocator);
    std::string OrderType(1, pOrderField.OrderType);
    document.AddMember("OrderType", rapidjson::Value(OrderType.c_str(), allocator), allocator);
    document.AddMember("UserProductInfo", rapidjson::Value(pOrderField.UserProductInfo, allocator).Move(), allocator);
    std::string ForceCloseReason(1, pOrderField.ForceCloseReason);
    document.AddMember("ForceCloseReason", rapidjson::Value(ForceCloseReason.c_str(), allocator), allocator);
    document.AddMember("CreditQuotaID", rapidjson::Value(pOrderField.CreditQuotaID, allocator).Move(), allocator);
    std::string CreditQuotaType(1, pOrderField.CreditQuotaType);
    document.AddMember("CreditQuotaType", rapidjson::Value(CreditQuotaType.c_str(), allocator), allocator);
    document.AddMember("CreditDebtID", rapidjson::Value(pOrderField.CreditDebtID, allocator).Move(), allocator);
    document.AddMember("IPAddress", rapidjson::Value(pOrderField.IPAddress, allocator).Move(), allocator);
    document.AddMember("MacAddress", rapidjson::Value(pOrderField.MacAddress, allocator).Move(), allocator);
    document.AddMember("RtnFloatInfo", rapidjson::Value(pOrderField.RtnFloatInfo), allocator);
    document.AddMember("RtnIntInfo", rapidjson::Value(pOrderField.RtnIntInfo), allocator);
    std::string ExtendOrderStatus(1, pOrderField.ExtendOrderStatus);
    document.AddMember("ExtendOrderStatus",rapidjson::Value(pOrderField.ExtendOrderStatus), allocator);


    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();

}


/**
 * д��JSON
 * @param json
 * @return
 */
std::string PsiCfgLoader::writeTraderStatusJson(int status, const char* traderId, const char* msg){
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("accountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("connectStatus", rapidjson::Value(status), allocator);
    document.AddMember("type", rapidjson::Value(1), allocator);
    document.AddMember("failedMsg", rapidjson::Value(ChartoUTF8(msg), allocator).Move(), allocator);
    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

/**
 * д��JSON
 * @param json
 * @return
 */
std::string PsiCfgLoader::writeTraderAccountJson(double usefullMoney, double totalMarketValue, double todayProfit, uint32_t accountDate, const char* traderId, const char* accountType, int status, const char* msg){
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("accountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("amountAvailable", rapidjson::Value(usefullMoney), allocator);
    document.AddMember("stockTotalMarketValue", rapidjson::Value(totalMarketValue), allocator);
    document.AddMember("todayProfit", rapidjson::Value(todayProfit), allocator);
    document.AddMember("accountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("accountType", rapidjson::Value(accountType, allocator), allocator);
    document.AddMember("status", rapidjson::Value(status), allocator);
    document.AddMember("msg", rapidjson::Value(ChartoUTF8(msg), allocator).Move(), allocator);
    document.AddMember("type", rapidjson::Value(2), allocator);
    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

/**
 * д��JSON
 * @param pInvestorRealTimeCreditInfo
 * @param accountDate
 * @param traderId
 * @param accountType
 * @return
 */
std::string PsiCfgLoader::writeCreditTraderAccountJson(TORACREDITAPI::CTORATstpInvestorRealTimeCreditInfoField* pInvestorRealTimeCreditInfo,uint32_t accountDate, const char* traderId, const char* accountType, int status, const char* msg){
    rapidjson::Document document;
    document.SetObject();

    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
    document.AddMember("accountId", rapidjson::Value(traderId, allocator).Move(), allocator);
    document.AddMember("amountAvailable", rapidjson::Value(pInvestorRealTimeCreditInfo->UsefulMoney), allocator);
    document.AddMember("stockTotalMarketValue", rapidjson::Value(pInvestorRealTimeCreditInfo->StockMarketValue), allocator);
    document.AddMember("todayProfit", rapidjson::Value(0), allocator);
    document.AddMember("collateralRatio", rapidjson::Value(pInvestorRealTimeCreditInfo->CollateralRatio), allocator);
    document.AddMember("usefulMargin", rapidjson::Value(pInvestorRealTimeCreditInfo->UsefulMargin), allocator);
    document.AddMember("creditBuyDebt", rapidjson::Value(pInvestorRealTimeCreditInfo->CreditBuyDebt), allocator);
    document.AddMember("creditBuyProfit", rapidjson::Value(pInvestorRealTimeCreditInfo->CreditBuyProfit), allocator);
    document.AddMember("interestFee", rapidjson::Value(pInvestorRealTimeCreditInfo->InterestFee), allocator);
    document.AddMember("totalAsset", rapidjson::Value(pInvestorRealTimeCreditInfo->TotalAsset), allocator);
    document.AddMember("totalDebt", rapidjson::Value(pInvestorRealTimeCreditInfo->TotalDebt), allocator);
    document.AddMember("status", rapidjson::Value(status), allocator);
    document.AddMember("msg", rapidjson::Value(ChartoUTF8(msg), allocator).Move(), allocator);
    document.AddMember("accountDate", rapidjson::Value(accountDate), allocator);
    document.AddMember("accountType", rapidjson::Value(accountType, allocator), allocator);
    document.AddMember("type", rapidjson::Value(2), allocator);
    rapidjson::StringBuffer buffer;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);

    return buffer.GetString();
}

#include "yaml.h"
bool yaml_to_variant(const YAML::Node& root, PsiVariant* params)
{
	if (root.IsNull() && params->type() != PsiVariant::VT_Object)
		return false;

	if (root.IsSequence() && params->type() != PsiVariant::VT_Array)
		return false;

	bool isMap = root.IsMap();
	for (auto& m : root)
	{
		std::string key = isMap ? m.first.as<std::string>() : "";
		const YAML::Node& item = isMap ? m.second : m;
		switch (item.Type())
		{
		case YAML::NodeType::Map:
		{
            PsiVariant* subObj = PsiVariant::createObject();
			if (yaml_to_variant(item, subObj))
			{
				if(isMap)
					params->append(key.c_str(), subObj, false);
				else
					params->append(subObj, false);
			}
		}
		break;
		case YAML::NodeType::Sequence:
		{
            PsiVariant* subAy = PsiVariant::createArray();
			if (yaml_to_variant(item, subAy))
			{
				if (isMap)
					params->append(key.c_str(), subAy, false);
				else
					params->append(subAy, false);
			}
		}
		break;
		case YAML::NodeType::Scalar:
			if (isMap)
				params->append(key.c_str(), item.as<std::string>().c_str());
			else
				params->append(item.as<std::string>().c_str());
			break;
		}
	}

	return true;
}

PsiVariant* PsiCfgLoader::load_from_yaml(const char* content)
{
	YAML::Node root = YAML::Load(content);

	if (root.IsNull())
		return NULL;

    PsiVariant* ret = PsiVariant::createObject();
	if (!yaml_to_variant(root, ret))
	{
		ret->release();
		return NULL;
	}

	return ret;
}

PsiVariant* PsiCfgLoader::load_from_content(const std::string& content, bool isYaml /* = false */)
{
	//��һ���Զ���������߼�
	bool isUTF8 = EncodingHelper::isUtf8((unsigned char*)content.data(), content.size());

	std::string buffer;
	//Linux�µ���UTF8
	//Win�µ���GBK
#ifdef _WIN32
	if (isUTF8)
		buffer = UTF8toChar(content);
#else
	if (!isUTF8)
		buffer = ChartoUTF8(content);
#endif

	if (buffer.empty())
		buffer = content;

	if (isYaml)
		return load_from_yaml(buffer.c_str());
	else
		return load_from_json(buffer.c_str());
}

PsiVariant* PsiCfgLoader::load_from_file(const char* filename)
{
	if (!StdFile::exists(filename))
		return NULL;

	std::string content;
	StdFile::read_file_content(filename, content);
	if (content.empty())
		return NULL;

	//��һ���Զ���������߼�
	bool isUTF8 = EncodingHelper::isUtf8((unsigned char*)content.data(), content.size());

	//By Wesley @ 2022.01.07
	//Linux�µ���UTF8
	//Win�µ���GBK
#ifdef _WIN32
	if(isUTF8)
		content = UTF8toChar(content);
#else
	if (!isUTF8)
		content = ChartoUTF8(content);
#endif

	if (StrUtil::endsWith(filename, ".json"))
		return load_from_json(content.c_str());
	else if (StrUtil::endsWith(filename, ".yaml") || StrUtil::endsWith(filename, ".yml"))
		return load_from_yaml(content.c_str());

	return NULL;
}
