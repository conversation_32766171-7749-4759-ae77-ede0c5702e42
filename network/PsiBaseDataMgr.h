/*!
 * \file PsiBaseDataMgr.cpp
 * \project	PsiTraderMagicWeapon
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 存储基础数据的管理类
 */
#pragma once

#include "PsiDataDef.hpp"
#include "PsiContractInfo.hpp"
#include "PsiSessionInfo.hpp"
#include "PsiVariant.hpp"
#include "StrUtil.hpp"
#include "TimeUtils.hpp"
#include <boost/filesystem.hpp>
#include "PsiMarcos.h"
#include "PsiTimeLoader.h"
#include "TORATstpCreditUserApiStruct.h"
#include "TORATstpLev2ApiStruct.h"
// #include "fmtlog-inl.h"


typedef PsiHashMap<std::string>		PsiContractList;
typedef PsiHashMap<std::string>		PsiExchgContract;
typedef PsiHashMap<std::string>		PsiContractMap;

typedef PsiHashMap<std::string>		PsiSessionMap;
typedef PsiHashMap<std::string>		PsiCommodityMap;

typedef psi_hashmap<std::string, CodeSet> SessionCodeMap;
class PsiBaseDataMgr {
public:
    PsiBaseDataMgr();
    ~PsiBaseDataMgr();
    /**
     * 初始化
     * @param psiTimeLoader
     * @return
     */
    bool init(PsiTimeLoader* psiTimeLoader);
public:
    /**
     * 从文件加载基础数据
     * @param filename
     * @return
     */
    bool		loadSessions(const char* filename);

    /**
     * 从文件加载假期列表
     * @param filename
     * @return
     */
    bool		loadHolidays(const char* filename);

    /**
     * 从JSON数据加载基础数据
     * @param content
     * @return
     */
    bool		loadJsonCommodities(const char* content);

    /**
     * 从JSON数据加载品种列表
     * @param content
     * @return
     */
    bool   editJsonCommodities(const char* content);

    /**
     * 从JSON数据加载品种列表
     * @param content
     * @return
     */
    bool		loadJsonSessions(const char* content);

    /**
     * 从JSON数据加载假期列表
     * @param content
     * @return
     */
    bool		loadJsonHolidays(const char* content);

    /**
     * 从JSON数据加载合约列表
     * @param content
     * @return
     */
    bool		loadJsonContracts(const char* content);

    /**
     * 获取品种的交易时间
     * @param sid
     * @return
     */
    PsiSessionInfo*	getSession(const char* sid);

    /**
     * 通过code 获取当前股票的所有信息
     * @param code
     * @return
     */
    PsiContractInfo*	getContract(const char* code);

    /**
     * 获取所有股票
     * @param exchg
     * @return
     */
    PsiArray*	getContracts(const char* exchg = "");

    /**
     * 通过code获取当前股票的占位index
     * @param code
     * @return
     */
    int        getContractIndex(const char* code);

    /**
     * 通过注册的行情ID获取当前行情的所有参数
     * @param parserId
     * @return
     */
    PSIParserParamsStruct& getParserParams(const char* parserId);

    /**
     * 设置行情模块的解析参数
     * @param parserId
     * @param params
     */
    void setParserParams(const char* parserId, PSIParserParamsStruct params);

    /**
     * 通过注册的交易ID获取当前交易的所有参数
     * @param traderId
     * @return
     */
    PSITraderParamsStruct& getTraderParams(const char* traderId);

    /**
     * 设置交易模块的解析参数
     * @param traderId
     * @param params
     */
    void setTraderParams(const char* traderId, PSITraderParamsStruct params);

    /**
     * 通过RunnerId获取当前运行的所有参数
     * @param runnerId
     * @return
     */
    PSIRunnerParamsStruct& getRunnerParams(const char* runnerId);

    /**
     * 设置运行子模块的解析参数
     * @param runnerId
     * @param params
     */
    void setRunnerParams(const char* runnerId, PSIRunnerParamsStruct params);

    /**
     * 获取资源调度的数据结构
     * @return
     */
    PSIResourceScheduleParamsStruct& getResourceScheduleParamsStruct();

    /**
     * 获取Runner的配置信息
     * @return
     */
    PSIRunnerConfigStruct& getRunnerConfig();

    /**
     * 获取策略的配置信息
     * @return
     */
    PSIStrategyConfigStruct& getStrategyConfig();

    /**
     * 通过注册的策略获取当前策略的所有参数
     * @param straCode
     * @return
     */
    PSIStrategyParamsStruct& getStrategyParams(const char* straCode);

    /**
     * 设置策略的解析参数
     * @param straCode
     * @param params
     */
    void setStrategyParams(const char* straCode, PSIStrategyParamsStruct params);

    /**
     * 通过注册的策略获取当前策略的所有参数
     * @param straCode
     * @return
     */
    PSIStrategyParamsStruct& getSellStrategyParams(const char* straCode);

    /**
     * 设置策略的解析参数
     * @param straCode
     * @param params
     */
    void setSellStrategyParams(const char* straCode, PSIStrategyParamsStruct params);

    /**
     * 通过注册的redisID获取当前行情的所有参数
     * @param parserId
     * @return
     */
    PSIRedisParamsStruct& getRedisParams(const char* redisId);

    /**
     * 设置redis模块的解析参数
     * @param parserId
     * @param params
     */
    void setRedisParams(const char* redisId, PSIRedisParamsStruct params);

    /**
     * 获取策略列表
     * @return
     */
    std::string getStra(const char* straCode);

    /**
     * 新增策略列表
     * @param straCode
     * @param straName
     */
    void addStraMap(const char* straCode, const char* straName);

    /**
     * 获取卖出策略列表
     * @return
     */
    std::string getSellStra(const char* straCode);

    /**
     * 新增卖出策略列表
     * @param straCode
     * @param straName
     */
    void addSellStraMap(const char* straCode, const char* straName);

    /**
     * 设置 最大的合约index
     * @param index
     */
    void setMaxContractIndex(int index);

    /**
     * 获取股票列表指针地址
     */
    psi_hashmap<std::string,int>& getCodes();

    /**
     * 新增RunnerId
     * @return
     */
    inline void addRunnerId(const char* runnerId){
        m_runnerIds.push_back(runnerId);
    }

    /**
     * 新增TraderId
     * @return
     */
    inline void addTraderId(const char* traderId){
        m_traderIds.push_back(traderId);
    }

    /**
     * 新增ParserId
     * @return
     */
    inline void addParserId(const char* parserId){
        m_parserIds.push_back(parserId);
    }
    /**
     * 生成请求ID
     * @return
     */
    inline uint32_t	genRequestID(){
       return m_reqid.fetch_add(1);
    }
      /**
     * 新增策略Key
     * @return
     */
    inline void addStrategyKey(const char* straCode){
        m_strategyKeys.push_back(straCode);
    }

    /**
     * 新增策略Key
     * @return
     */
    inline void addSellStrategyKey(const char* straCode){
        m_sellStrategyKeys.push_back(straCode);
    }

    /**
     * 新增redisId
     * @return
     */
    inline void addRedisId(const char* redisId){
        m_redisIds.push_back(redisId);
    }

    /**
     * 获取RunnerId集合
     * @return
     */
    inline std::vector<std::string>& getRunnerIds(){
        return m_runnerIds;
    }

    /**
     * 获取TraderId集合
     * @return
     */
    inline std::vector<std::string>& getTraderIds(){
        return m_traderIds;
    }

    /**
     * 获取ParserId集合
     * @return
     */
    inline std::vector<std::string>& getParserIds(){
        return m_parserIds;
    }

    /**
     * 获取策略Key集合
     * @return
     */
    inline std::vector<std::string>& getStrategyKeys(){
        return m_strategyKeys;
    }

    /**
     * 获取策略Key集合
     * @return
     */
    inline std::vector<std::string>& getSellStrategyKeys(){
        return m_sellStrategyKeys;
    }

    /**
     * 获取redisId集合
     * @return
     */
    inline std::vector<std::string>& getRedisIds(){
        return m_redisIds;
    }

    /**
     * 获取当前时间 纳秒
     * @return
     */
    inline int64_t getNowTime(){
        return mp_timeLoader->getCurTime();
    }

    /**
     * 获取当前时间 纳秒
     * @return
     */
    inline uint64_t getCurNowTime(){
        return mp_timeLoader->getCurNowTime();
    }

    /**
     * 通过注册的redisID获取当前行情的所有参数
     * @param parserId
     * @return
     */
    TORACREDITAPI::CTORATstpBrokerCreditSecurityField& getBrokerCreditSecurity(const char* stdCode);

    /**
     * 设置redis模块的解析参数
     * @param parserId
     * @param params
     */
    void setBrokerCreditSecurity(const char* stdCode, TORACREDITAPI::CTORATstpBrokerCreditSecurityField params);

private:
    SessionCodeMap		m_mapSessionCode; // 存储交易时间段的映射数据
    PsiExchgContract*	m_mapExchgContract; // 存储交易所对应合约的映射数据
    PsiSessionMap*		m_mapSessions; // 存储交易时间段的映射数据
    PsiCommodityMap*	m_mapCommodities; //存储品种列表
    PsiContractMap*		m_mapContracts; //存储合约列表
    psi_hashmap<std::string, int> m_mapCodes; //存储假期列表

    psi_hashmap<std::string, PSIParserParamsStruct> m_mapParserParams; // 存储行情模块解析参数

    psi_hashmap<std::string, PSITraderParamsStruct> m_mapTraderParams; // 存储交易模块解析参数

    psi_hashmap<std::string, PSIRunnerParamsStruct> m_mapRunnerParams; // 存储Runner模块解析参数

    PSIStrategyConfigStruct m_strategyConfig; // 策略配置信息

    psi_hashmap<std::string, std::string> m_straMap; // 策略列表

    psi_hashmap<std::string, PSIStrategyParamsStruct> m_mapStrategyParams; // 存储策略模块解析参数

    psi_hashmap<std::string, std::string> m_sellStraMap; // 策略列表

    psi_hashmap<std::string, PSIStrategyParamsStruct> m_mapSellStrategyParams; // 存储策略模块解析参数

    PSIResourceScheduleParamsStruct m_resourceScheduleParams; // 资源调度参数

    PSIRunnerConfigStruct m_runnerConfig; // Runner配置信息

    // int* m_codes; // 股票列表

    int max_contract_index = 0; // 最大合约index

    psi_hashmap<std::string,int> m_codes; // 股票列表

    std::vector<std::string> m_runnerIds; // runnerID集合
    std::vector<std::string> m_traderIds; // traderID集合
    std::vector<std::string> m_parserIds; // parserID集合
    std::vector<std::string> m_strategyKeys; // 策略Key集合
    std::vector<std::string> m_sellStrategyKeys; // 策略Key集合
    std::vector<std::string> m_redisIds; // redisID集合

    std::atomic<uint32_t>		m_reqid;

    psi_hashmap<std::string, PSIRedisParamsStruct> m_mapRedisParams; // 存储redis相关参数

    PsiTimeLoader* mp_timeLoader; // 时间加载器

    psi_hashmap<std::string, TORACREDITAPI::CTORATstpBrokerCreditSecurityField> m_mapBrokerCreditSecurity; // 存储券商信用证券信息


public:
//    TORALEV2API::CTORATstpLev2OrderDetailField **m_shOrderDetails;
//    PSILev2OrderDetailSubStruct **m_shOrderDetailSub;
//    int **m_shOrderDetails;
//    std::atomic<int> m_shOrderDetailIndex;

//    TORALEV2API::CTORATstpLev2OrderDetailField **m_szOrderDetails;
//    PSILev2OrderDetailSubStruct **m_szOrderDetailSub;
//    int **m_szOrderDetails;
//    std::atomic<int> m_szOrderDetailIndex;

//    TORALEV2API::CTORATstpLev2TransactionField **m_shTransactionDetails;
//    PSILev2TransactionSubStruct **m_shTransactionDetailSub;
//    int **m_shTransactionDetails;
//    std::atomic<int> m_shTransactionDetailIndex;

//    TORALEV2API::CTORATstpLev2TransactionField **m_szTransactionDetails;
//    PSILev2TransactionSubStruct **m_szTransactionDetailSub;
//    int **m_szTransactionDetails;
//    std::atomic<int> m_szTransactionDetailIndex;
};



