# network/CMakeLists.txt
cmake_minimum_required(VERSION 3.10)

# 收集所有源文件
file(GLOB_RECURSE PSI_SOURCES
    "*.cpp"
    "*.hpp"
    "*.tpp"
    "*.c"
    "*.h"
)

# 创建网络库
add_library(PsiNetwork STATIC ${PSI_SOURCES})

# 添加包含路径
target_include_directories(PsiNetwork PUBLIC
   ${CMAKE_CURRENT_SOURCE_DIR}
)

# 添加编译定义
target_compile_definitions(PsiNetwork PRIVATE
    BOOST_ALL_NO_LIB
)

# 依赖：Boost
find_package(Boost REQUIRED COMPONENTS filesystem system)
if(Boost_FOUND)
  target_include_directories(PsiNetwork PRIVATE ${Boost_INCLUDE_DIRS})
  target_link_libraries(PsiNetwork PRIVATE ${Boost_LIBRARIES})
endif()

# 可选：RapidJSON（header-only）
find_package(RapidJSON QUIET)
if(RapidJSON_FOUND)
  target_include_directories(PsiNetwork PRIVATE ${RapidJSON_INCLUDE_DIRS})
endif()

# atomic_queue（header-only）- 自动查找
find_path(ATOMIC_QUEUE_INCLUDE_DIR
  NAMES atomic_queue/atomic_queue.h
  PATHS
    "${CMAKE_SOURCE_DIR}/atomic_queue/include"
    "${CMAKE_CURRENT_SOURCE_DIR}/../atomic_queue/include"
  NO_DEFAULT_PATH
)

if(ATOMIC_QUEUE_INCLUDE_DIR)
  target_include_directories(PsiNetwork PUBLIC "${ATOMIC_QUEUE_INCLUDE_DIR}")
  message(STATUS "Found atomic_queue at ${ATOMIC_QUEUE_INCLUDE_DIR}")
else()
  message(FATAL_ERROR "atomic_queue not found! Please run: git clone https://github.com/max0x7ba/atomic_queue.git")
endif()

# 可选：hiredis（使用 pkg-config 检测）
find_package(PkgConfig QUIET)
if(PKG_CONFIG_FOUND)
  pkg_check_modules(HIREDIS hiredis QUIET)
  if(HIREDIS_FOUND)
    target_compile_definitions(PsiNetwork PRIVATE HAS_HIREDIS=1)
    target_include_directories(PsiNetwork PRIVATE ${HIREDIS_INCLUDE_DIRS})
    target_link_libraries(PsiNetwork PRIVATE ${HIREDIS_LIBRARIES})
  endif()
endif()

# Windows 特定设置
if(WIN32)
    target_link_libraries(PsiNetwork PRIVATE ws2_32)
endif()
