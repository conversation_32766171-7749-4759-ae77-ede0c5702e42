/*!
 * \file ParserHuaX.h
 * \project	PsiTraderMagicWeapon
 *
 * \author l<PERSON><PERSON>
 * \date 2024/03/07
 *
 * \brief
 */
#pragma once
#ifdef HAS_HIREDIS
#include <hiredis.h>
#else
struct redisContext;
struct redisReply;
#endif
#include <boost/asio.hpp>
#include <boost/asio/executor_work_guard.hpp>

#include "PsiMarcos.h"
#include "PsiVariant.hpp"

#include "StdUtils.hpp"
#include "PsiStruct.h"
#include "PsiBaseDataMgr.h"
#include "PsiRedisDataMgr.h"

class PsiRedis {
public:
    PsiRedis();
    ~PsiRedis();

    typedef enum
    {
        TS_NOTLOGIN,		//未登录
        TS_LOGINING,		//正在登录
        TS_LOGINED,			//已登录
        TS_LOGINFAILED,		//登录失败
        TS_ALLREADY			//全部就绪
    } ThirdPartyDataState;

public:
    /**
     * 初始化
     * @param pBaseDataMgr
     * @param pRedisDataMgr
     * @return
     */
    bool init(PsiBaseDataMgr* pBaseDataMgr, PsiRedisDataMgr* pRedisDataMgr);

    /**
     * 运行
     * @return
     */
    bool run();

    /**
     * 读取Key对应的内容
     * @param key
     * @return
     */
    std::string getContentByKey(const char* key);

    /**
     * 写数据
     * @param rediskey
     * @param mapkey
     * @param mapvalue
     * @return
     */
    int HSet(const std::string rediskey, const std::string mapkey, const std::string mapvalue);

    /**
     * 删除Redis Key
     * @param rediskey
     * @return
     */
    int delKey(const std::string rediskey);

    /**
     * 推送消息
     * @param channel
     * @param content
     * @return
     */
    int pushContent(const char* channel = "", const char* content = "");


private:

    /**
     * 登录
     */
    void		doLogin();

    /**
     * 订阅
     */
    void subscriptionLoop();

    /**
     * 订阅回调
     */
    void subscribeCallback();

    /**
     * 订阅
     */
    void subscriptionTraderLoop();

    /**
     * 订阅回调
     */
    void subscribeTraderCallback();


private:

    PsiBaseDataMgr* mp_baseDataMgr; // 基础数据管理类
    PsiRedisDataMgr* mp_redisDataMgr; // redis数据管理类

    // 定义redis
    redisContext*   mp_redisContext; // redis上下文
    redisReply*     mp_redisReply; // redis回复
    redisReply*     mp_pingRedisReply; // ping redis回复
    // 定义订阅相关的参数
    redisContext*   mp_subscribeRedisContext; // 订阅redis上下文
    redisReply*     mp_subscribeRedisReply; // 订阅redis回复
    // 定义订阅交易相关的参数
    redisContext*   mp_subscribeTraderRedisContext; // 订阅redis上下文
    redisReply*     mp_subscribeTraderRedisReply; // 订阅redis回复

    std::string		m_user;
    std::string		m_pass;
    std::string		m_front;
    std::string    m_channel;
    std::string    m_traderChannel; // 监听交易发来的topic
    int				m_port;
    bool			m_inited;
    uint32_t        m_cpuCore=0; // 核心数
    uint32_t        m_traderCpuCore=0; // 交易监听核心数

#if BOOST_VERSION >= 106600
    boost::asio::io_context m_asyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_work{m_asyncio.get_executor()};
#else
    boost::asio::io_service m_asyncio;
    std::unique_ptr<boost::asio::io_service::work> m_work;
#endif
    StdThreadPtr				m_thrdWorker;
    StdThreadPtr                m_thrdSubWorker;
    StdThreadPtr                m_thrdTraderWorker;
    StdThreadPtr                m_thrdPushWorker;

};

