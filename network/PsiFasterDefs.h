#pragma once
#include <string.h>
#include "PsiMarcos.h"

#include "unordered_dense.h"

 /*
  *	By <PERSON><PERSON><PERSON> @ 2024.02.05
  *	ankerlд���ٶȱ�robin�úܶ࣬��ſ�1/3��������������40w���ڵ�ʱ��
  *	����robin�Ķ�ȡ�ٶȱ�robin�ã���������30w���������ڣ����Ͳ���
  *	����wondertrader�ĳ���������ankerlҪ�úܶ�
  * ������Բο�����ҳ������ܶԱ�
  * https://martin.ankerl.com/2022/08/27/hashmap-bench-01/#benchmark-results-table
  */


struct string_hash
{
	//BKDRHash�㷨
	std::size_t operator()(const std::string& key) const
	{
		size_t seed = 131; // 31 131 1313 13131 131313 etc..
		size_t hash = 0;

		char* str = (char*)key.c_str();
		while (*str)
		{
			hash = hash * seed + (*str++);
		}

		return (hash & 0x7FFFFFFF);
	}
};

//////////////////////////////////////////////////////////////////////////
//����ʹ��unordered_dense

template<class Key, class T, class Hash = ankerl::unordered_dense::hash<Key>>
class psi_hashmap : public ankerl::unordered_dense::map<Key, T, Hash>
{
public:
	typedef ankerl::unordered_dense::map<Key, T, Hash>	Container;
    psi_hashmap() :Container() {}
};

template<class T>
class psi_hashmap<std::string, T, string_hash> : public ankerl::unordered_dense::map<std::string, T, string_hash>
{
public:
	typedef ankerl::unordered_dense::map<std::string, T, string_hash>	Container;
    psi_hashmap() :Container() {}
};

template<class Key, class Hash = std::hash<Key>>
class psi_hashset : public ankerl::unordered_dense::set<Key, Hash>
{
public:
	typedef ankerl::unordered_dense::set<Key, Hash>	Container;
    psi_hashset() :Container() {}
};

template<>
class psi_hashset<std::string, string_hash> : public ankerl::unordered_dense::set<std::string, string_hash>
{
public:
	typedef ankerl::unordered_dense::set<std::string, string_hash>	Container;
    psi_hashset() :Container() {}
};

typedef psi_hashset<std::string,string_hash> CodeSet;

