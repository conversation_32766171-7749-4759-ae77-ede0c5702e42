/*!
 * \file TraderHuaX.h
 * \project	PsiTraderMagicWeapon
 *
 * \author l<PERSON><PERSON>
 * \date 2024/02/05
 *
 * \brief
 */
#pragma once

#include <stdint.h>
#include <string>
#include <boost/asio.hpp>
// Boost.Asio 版本兼容性：老版本无 executor_work_guard
#if BOOST_VERSION >= 106600
#include <boost/asio/executor_work_guard.hpp>
#else
#include <boost/asio/io_service.hpp>
#endif

#include "PsiCollection.hpp"

#include "StdUtils.hpp"
#include "PsiKVCache.hpp"

#include "PsiContractInfo.hpp"
#include "PsiVariant.hpp"
#include "StrUtil.hpp"
#include "TimeUtils.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiTraderDataMgr.h"
#include "PsiRedisDataMgr.h"
#include "PsiDataMgr.h"
#include <boost/format.hpp>

#include <boost/filesystem.hpp>

#include "TORATstpCreditTraderApi.h"



typedef TORACREDITAPI::CTORATstpTraderSpi HuaXCreditTraderSpi;
typedef TORACREDITAPI::CTORATstpTraderApi HuaXCreditTraderApi;



class TraderHuaXCredit : public HuaXCreditTraderSpi
{
public:
    TraderHuaXCredit();
	~TraderHuaXCredit();

public:
	//////////////////////////////////////////////////////////////////////////
	//HuaX::API::TraderSpi �ӿ�
	virtual void OnFrontConnected() override;
	///���ͻ����뽻�׺�̨ͨ�����ӶϿ�ʱ���÷��������á���������������API���Զ��������ӣ��ͻ��˿ɲ���������
	///@param nReason ����ԭ��
	///        -3 �����ѶϿ�
	///        -4 �����ʧ��
	///        -5 ����дʧ��
	///        -6 ����������
	///        -7 ����Ŵ���
	///        -8 �������������
	///        -9 ����ı���
	///		  -15 �����ʧ��
	///		  -16 ����дʧ��
	virtual void OnFrontDisconnected(int nReason) override;

    ///����Ӧ��
	virtual void OnRspError(TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ȡ������ϢӦ��
    virtual void OnRspGetConnectionInfo(TORACREDITAPI::CTORATstpConnectionInfoField *pConnectionInfoField, TORACREDITAPI::CTORATstpRspInfoField *pRspInfo, int nRequestID) override;

    ///��¼��Ӧ
    virtual void OnRspUserLogin(TORACREDITAPI::CTORATstpRspUserLoginField* pRspUserLoginField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///�ǳ���Ӧ
    virtual void OnRspUserLogout(TORACREDITAPI::CTORATstpUserLogoutField *pUserLogoutField, TORACREDITAPI::CTORATstpRspInfoField *pRspInfoField, int nRequestID) override;

    ///����¼����Ӧ
    virtual void OnRspOrderInsert(TORACREDITAPI::CTORATstpInputOrderField* pInputOrderField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///�����ر�
	virtual void OnRtnOrder(TORACREDITAPI::CTORATstpOrderField* pOrderField) override;

    ///��������ر�
	virtual void OnErrRtnOrderInsert(TORACREDITAPI::CTORATstpInputOrderField* pInputOrderField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///�ɽ��ر�
	virtual void OnRtnTrade(TORACREDITAPI::CTORATstpTradeField* pTradeField) override;

    ///������Ӧ
    virtual void OnRspOrderAction(TORACREDITAPI::CTORATstpInputOrderActionField *pInputOrderActionField, TORACREDITAPI::CTORATstpRspInfoField *pRspInfoField, int nRequestID) override;

    ///��������ر�
    virtual void OnErrRtnOrderAction(TORACREDITAPI::CTORATstpInputOrderActionField* pInputOrderActionField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID) override;

    ///��ѯ������Ӧ
	virtual void OnRspQryOrder(TORACREDITAPI::CTORATstpOrderField* pOrderField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯ�ɽ���Ӧ
	virtual void OnRspQryTrade(TORACREDITAPI::CTORATstpTradeField* pTradeField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯͶ���ֲ߳���Ӧ
	virtual void OnRspQryPosition(TORACREDITAPI::CTORATstpPositionField* pPositionField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯ�ʽ��˻���Ӧ
	virtual void OnRspQryTradingAccount(TORACREDITAPI::CTORATstpTradingAccountField* pTradingAccountField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯͶ����ʵʱ������ȯ��Ϣ����
    virtual void OnRspQryInvestorRealTimeCreditInfo(TORACREDITAPI::CTORATstpInvestorRealTimeCreditInfoField *pInvestorRealTimeCreditInfo, TORACREDITAPI::CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    ///��ѯ�ɶ��˻���Ӧ
	virtual void OnRspQryShareholderAccount(TORACREDITAPI::CTORATstpShareholderAccountField* pShareholderAccountField, TORACREDITAPI::CTORATstpRspInfoField* pRspInfoField, int nRequestID, bool bIsLast) override;

    ///��ѯ��˾�ʸ�֤ȯ
    virtual void OnRspQryBrokerCreditSecurity(TORACREDITAPI::CTORATstpBrokerCreditSecurityField *pBrokerCreditSecurity, TORACREDITAPI::CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;
public:
	//////////////////////////////////////////////////////////////////////////
	//ITraderApi �ӿ�
	bool init();

	void release();

	void registerSpi(PsiBaseDataMgr* pBdMgr, PsiDataMgr *dataMgr, PsiTraderDataMgr* pTdMgr, PsiRedisDataMgr* pRdMgr);

	void connect();

	void disconnect();

	bool isConnected();

	bool makeEntrustID(char* buffer, int length);

	int login();

	int logout();

    /*
     *	�µ��ӿ�(����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderBuy(int orderRef, const char* code, const char* exch, const char* userTag, int64_t volume, double price);

    /*
     *	�µ��ӿ�(����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderSell(int orderRef, const char* code, const char* exch, const char* userTag, int64_t volume, double price);

    /*
     *	�µ��ӿ�(����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderCancel(int orderRef, const char* orderSysId, const char* exch);

	int orderAction();

	int queryAccount();

    int queryInvestorRealTimeCreditInfo();

	int queryPositions();

    int queryCodePositions(const char* code);

	int queryOrders();

	int queryTrades();

    int queryBrokerCreditSecurity(const char* code, const char* exch);

private:
	void		reconnect();
	void					doLogin();
	inline bool	extractEntrustID(const char* entrustid, int &orderRef);
	inline void	genEntrustID(char* buffer, uint32_t orderRef);

private:
    HuaXCreditTraderApi*	m_api;

	typedef PsiHashMap<std::string> PositionMap;
	PsiArray*				m_positions;
    PsiArray*				m_trades;
    PsiArray*				m_orders;
    PsiArray*				m_accounts;

	uint32_t		m_tradingday;

#if BOOST_VERSION < 106600
#undef m_asyncio
#undef m_work
#endif
    boost::asio::io_context m_asyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_work{m_asyncio.get_executor()};
	StdThreadPtr				m_thrdWorker;

    boost::asio::io_context m_traderAsyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_traderWork{m_traderAsyncio.get_executor()};
    StdThreadPtr				m_traderWorker;

    StdThreadPtr				m_queryWorker;

	//ί�е���ǻ�����
	WtKVCache		m_eidCache;
	//������ǻ�����
	WtKVCache		m_oidCache;

    PsiBaseDataMgr* mp_bdMgr; // �������ݹ�����
    PsiDataMgr*     mp_dataMgr; // ���ݹ�����
    PsiTraderDataMgr* mp_tdMgr; // �������ݹ�����
    PsiRedisDataMgr* mp_rdMgr; // Redis���ݹ�����
    bool m_bCheckTrade;    // �Ƿ��齻��ģ��
};

