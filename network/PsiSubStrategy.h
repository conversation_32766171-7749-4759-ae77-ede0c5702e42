/*!
 * \file PsiSubStrategy.cpp
 * \project	PsiSubStrategy
 *
* \author liji<PERSON>
* \date 2024/02/05
 *
 * \brief 策略线程子类
 */
#pragma once
#include "lockfree.hpp"
#include "StdUtils.hpp"
#include "PsiDataMgr.h"

class PsiSubStrategy {
public:
    PsiSubStrategy();
    ~PsiSubStrategy();

    /**
     * @brief 初始化
     * @param cpuCore
     * @return
     */
    bool init(PsiDataMgr* dataMgr, int cpuCore);

    /**
     * @brief 启动
     * @return
     */
    bool run();
public:
    lockfree::spsc::RingBuf<int, 1024U> m_threadQueue; // 策略线程队列
    std::atomic<int> m_threadStatus; // 当前线程状态 -1表示未运行 不等于-1表示运行中
    std::atomic_flag m_straThreadLock = ATOMIC_FLAG_INIT; // 策略锁
    uint32_t	m_straThreadId; // 策略线程ID
    int m_cpuCore = 0; // 计算线程绑定的CPU核心
    PsiDataMgr* mp_dataMgr; // 股票数据管理模块
    StdThreadPtr m_threadWorker; // 线程对象
};

