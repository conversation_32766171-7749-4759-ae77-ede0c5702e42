#pragma once

// 跨平台 mmap 封装：Windows 使用 CreateFileMapping；Linux/Unix 使用 mmap
#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <memoryapi.h>

template<class T>
T* MyMmap(const char* filename, bool /*use_shm*/, const char** error_msg, size_t size = sizeof(T)) {
    HANDLE hFile = CreateFileA(filename,
                               GENERIC_READ | GENERIC_WRITE,
                               FILE_SHARE_READ | FILE_SHARE_WRITE,
                               NULL,
                               OPEN_ALWAYS,
                               FILE_ATTRIBUTE_NORMAL,
                               NULL);
    if (hFile == INVALID_HANDLE_VALUE) {
        *error_msg = "CreateFile";
        return nullptr;
    }
    HANDLE hMap = CreateFileMapping(hFile, NULL, PAGE_READWRITE, 0, (DWORD)size, NULL);
    if (!hMap) {
        *error_msg = "CreateFileMapping";
        CloseHandle(hFile);
        return nullptr;
    }
    T* ret = (T*)MapViewOfFile(hMap, FILE_MAP_ALL_ACCESS, 0, 0, size);
    CloseHandle(hMap);
    CloseHandle(hFile);
    if (!ret) {
        *error_msg = "MapViewOfFile";
        return nullptr;
    }
    return ret;
}

template<class T>
void MyUnmap(void* addr, size_t /*size*/ = sizeof(T)) {
    UnmapViewOfFile(addr);
}

#else
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <cstring>

template<class T>
T* MyMmap(const char* filename, bool /*use_shm*/, const char** error_msg, size_t size = sizeof(T)) {
    int fd = ::open(filename, O_RDWR | O_CREAT, 0666);
    if (fd < 0) {
        *error_msg = "open";
        return nullptr;
    }
    struct stat st{};
    if (fstat(fd, &st) < 0) {
        *error_msg = "fstat";
        ::close(fd);
        return nullptr;
    }
    if ((size_t)st.st_size < size) {
        if (ftruncate(fd, (off_t)size) < 0) {
            *error_msg = "ftruncate";
            ::close(fd);
            return nullptr;
        }
    }
    void* addr = ::mmap(nullptr, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    ::close(fd);
    if (addr == MAP_FAILED) {
        *error_msg = "mmap";
        return nullptr;
    }
    return reinterpret_cast<T*>(addr);
}

template<class T>
void MyUnmap(void* addr, size_t size = sizeof(T)) {
    if (addr) ::munmap(addr, size);
}
#endif
