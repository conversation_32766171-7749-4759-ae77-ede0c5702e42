/*!
 * \file PsiDataMgr.cpp
 * \project	PsiDataMgr
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 股票数据管理模块
 */
#pragma once
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"
#include "lockfree.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiDataMgr.h"
#include "PsiSubData.h"

class PsiDataMgr {
public:
    PsiDataMgr();
    ~PsiDataMgr();

    bool init(PsiBaseDataMgr* pBaseDataMgr, PsiTimeLoader* psiTimeLoader, PSIRunnerConfigStruct runnerConfig); // 初始化


public:
    PsiSubData** m_stockDatas; // 股票数据指针

    int  m_stockCount; // 股票数量

    psi_hashmap<std::string,int> m_codes; // 股票列表

    PsiBaseDataMgr* mp_baseDataMgr; // 基础数据管理模块
    PsiTimeLoader* mp_timeLoader; // 存储时间加载类指针
};

