//
// Created by Administrator on 2024/2/8.
//

#include "PsiTraderDataMgr.h"
#include "TimeUtils.hpp"

inline uint32_t makeRefID(int time)
{
    static std::atomic<uint32_t> auto_refid(0);
    if(auto_refid == 0)
        auto_refid = (uint32_t)((TimeUtils::getLocalTimeNow() - TimeUtils::makeTime(time, 0)) / 1000);
    return auto_refid.fetch_add(1);
}

inline uint32_t makeLocalOrderID()
{
    static std::atomic<uint32_t> _auto_order_id{ 0 };
    if (_auto_order_id == 0)
    {
        uint32_t curYear = TimeUtils::getCurDate() / 10000 * 10000 + 101;
        _auto_order_id = (uint32_t)((TimeUtils::getLocalTimeNow() - TimeUtils::makeTime(curYear, 0)) / 1000 * 50);
    }

    return _auto_order_id.fetch_add(1);
}

PsiTraderDataMgr::PsiTraderDataMgr()
        : m_ordref(0)
        , m_autoOrderId(0)
        , m_inited(false)
        , m_terminal("PC")
        , m_encrypt(false)
        , m_proxy(false)
        , m_flowdir("HuaXTDFlow")
        , m_state(TS_UNLOGIN)
{
    // uint32_t auto_refid = (uint32_t)((TimeUtils::getLocalTimeNow() - TimeUtils::makeTime(20240101, 0)) / 1000);
    m_autoOrderId.store(makeLocalOrderID());
}

bool PsiTraderDataMgr::init(){
    m_ordref.store(makeRefID(m_orderefIndex));
    printf("[PsiTraderDataMgr] PsiTraderDataMgr Init Success! m_ordref[%d] m_reqid[%d]\n", m_ordref.load());
    return true;
}

PsiTraderDataMgr::~PsiTraderDataMgr()
{
}

/**
 * 通过注册的redisID获取当前行情的所有参数
 * @param parserId
 * @return
 */
TORACREDITAPI::CTORATstpBrokerCreditSecurityField& PsiTraderDataMgr::getBrokerCreditSecurity(const char* stdCode){
    auto it = m_mapBrokerCreditSecurity.find(stdCode);
    if (it == m_mapBrokerCreditSecurity.end()){
        TORACREDITAPI::CTORATstpBrokerCreditSecurityField params;
        return params;
    }
    return it->second;
}

/**
 * 设置redis模块的解析参数
 * @param parserId
 * @param params
 */
void PsiTraderDataMgr::setBrokerCreditSecurity(const char* stdCode, TORACREDITAPI::CTORATstpBrokerCreditSecurityField params){
    m_mapBrokerCreditSecurity.insert(std::make_pair(stdCode, params));
}
