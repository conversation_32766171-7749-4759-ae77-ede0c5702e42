/*!
 * \file PsiResourceSchedule.cpp
 * \project	PsiResourceSchedule
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 资源调度
 */
#pragma once
#include "PsiBaseDataMgr.h"
#include "PsiParserDataMgr.h"
#include "PsiTypes.h"
#include "PsiDataMgr.h"
#include "PsiSubCompute.h"
#include "PsiSubStrategy.h"


class PsiSubCompute;
class PsiSubStrategy;
class PsiResourceMgr {
public:
    PsiResourceMgr();
    ~PsiResourceMgr();

    /**
     * @brief 初始化
     * @param pDataMgr
     * @param pBaseDataMgr
     * @return
     */
    bool init(PsiDataMgr* pDataMgr, PsiBaseDataMgr* pBaseDataMgr, PsiParserDataMgr* parserData, std::vector<int> computeCpuCores, std::vector<int> straCpuCores);

    /**
     * @brief 启动
     * @return
     */
    bool run();

public:

//    lockfree::mpmc::Queue<int, 1024> m_computeQueue; // 计算线程资源调度队列
//
//    lockfree::mpmc::Queue<int, 1024> m_straQueue; // 策略线程资源调度队列

    // std::vector<PsiSubCompute*> m_computeThreads; // 计算线程列表
    PsiSubCompute** m_computeThreads; // 计算线程指针列表

    // std::vector<PsiSubStrategy*> m_straThreads; // 策略线程列表
    PsiSubStrategy** m_straThreads; // 策略线程指针列表

    std::atomic<int> m_straComputeIndex; // 策略当前计算线程下标

    std::vector<int> m_computeCpuCores; // 计算模块CPU核心列表

    std::vector<int> m_straCpuCores; // 策略模块CPU核心列表

    PsiDataMgr* mp_dataMgr; // 数据管理模块

    PsiBaseDataMgr* mp_baseDataMgr; // 基础数据管理模块

    PsiParserDataMgr* mp_parserDataMgr; // 行情数据存储

    int m_codes[MAX_CODE_SIZE]; // 股票代码列表

    int m_codeCount = 0; // 股票代码数量

    std::atomic<int64_t> m_computeCodeIndex; // 当前计算线程下标

    std::atomic_flag m_swapLock = ATOMIC_FLAG_INIT; // 计算股票下标锁
};

