/*!
 * \file TraderHuaX.h
 * \project	PsiTraderMagicWeapon
 *
 * \author liji<PERSON>
 * \date 2024/02/05
 * 
 * \brief 
 */
#pragma once

#include <stdint.h>
#include <string>
#include <boost/asio.hpp>
// Boost.Asio 版本兼容性：老版本无 executor_work_guard
#if BOOST_VERSION >= 106600
#include <boost/asio/executor_work_guard.hpp>
#else
#include <boost/asio/io_service.hpp>
#endif
#include "PsiCollection.hpp"
#include "StdUtils.hpp"
#include "PsiKVCache.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiTraderDataMgr.h"
#include "PsiRedisDataMgr.h"
#include "PsiDataMgr.h"

#include "ThostFtdcTraderApi.h"


typedef CThostFtdcTraderSpi CTPTraderSpi;
typedef CThostFtdcTraderApi CTPTraderApi;

class PsiTraderTcpShmSpi;

class TraderCTP : public CTPTraderSpi
{
public:
    TraderCTP();
	~TraderCTP();

public:
    virtual void OnFrontConnected() override;

    virtual void OnFrontDisconnected(int nReason) override;

    virtual void OnHeartBeatWarning(int nTimeLapse) override;

    virtual void OnRspAuthenticate(CThostFtdcRspAuthenticateField *pRspAuthenticateField, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspUserLogin(CThostFtdcRspUserLoginField *pRspUserLogin, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspUserLogout(CThostFtdcUserLogoutField *pUserLogout, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspSettlementInfoConfirm(CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspQrySettlementInfoConfirm(CThostFtdcSettlementInfoConfirmField *pSettlementInfoConfirm, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspQryTradingAccount(CThostFtdcTradingAccountField *pTradingAccount, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspOrderInsert(CThostFtdcInputOrderField *pInputOrder, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    ///��������������Ӧ
    virtual void OnRspOrderAction(CThostFtdcInputOrderActionField *pInputOrderAction, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspQryInvestorPosition(CThostFtdcInvestorPositionField *pInvestorPosition, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspQrySettlementInfo(CThostFtdcSettlementInfoField *pSettlementInfo, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    ///�����ѯ�ɽ���Ӧ
    virtual void OnRspQryTrade(CThostFtdcTradeField *pTrade, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspQryOrder(CThostFtdcOrderField *pOrder, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRspError(CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    virtual void OnRtnOrder(CThostFtdcOrderField *pOrder) override;

    virtual void OnRtnTrade(CThostFtdcTradeField *pTrade) override;

    virtual void OnErrRtnOrderInsert(CThostFtdcInputOrderField *pInputOrder, CThostFtdcRspInfoField *pRspInfo) override;

    virtual void OnRtnInstrumentStatus(CThostFtdcInstrumentStatusField *pInstrumentStatus) override;
public:
    /*
    *	��������Ϣ
    */
    bool IsErrorRspInfo(CThostFtdcRspInfoField *pRspInfo);
	//////////////////////////////////////////////////////////////////////////
	//ITraderApi �ӿ�
	bool init();

	void release();

	void registerSpi(PsiBaseDataMgr* pBdMgr, PsiDataMgr *dataMgr, PsiTraderDataMgr* pTdMgr, PsiRedisDataMgr* pRdMgr);

	/*
	 *   ����֪ͨ������TcpShmServer
	 */
	void registerTraderSpi(PsiTraderTcpShmSpi* pSpi);

	bool connect();

	void disconnect();

	bool isConnected();

	bool tradeStateMachine();

	int login();

	int logout();
    /*
     *	�µ��ӿ�(���룬����)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderInsert(PSIDoTraderStruct& orderStruct);
    /*
     *	�µ��ӿ�(�������ĵ�)
     *	entrust �µ��ľ������ݽṹ
     */
    int orderAction(PSIDoTraderStruct& orderStruct);


	int queryAccount();

	int queryPositions();

	int queryOrders();

	int queryTrades();

	int querySettlement(uint32_t uDate);

private:
	void  reconnect();

	void  authenticate();

    void  confirm();

    void  queryConfirm();

	void  wait();

private:
    CTPTraderApi*	m_api;

	typedef PsiHashMap<std::string> PositionMap;

	PsiArray*				m_positions;
    PsiArray*				m_trades;
    PsiArray*				m_orders;
    PsiArray*				m_accounts;

	uint32_t		m_tradingday;

	// StdThreadPtr				m_thrdWorker;
    StdThreadPtr				m_traderWorker;
    StdThreadPtr				m_queryWorker;

	//ί�е���ǻ�����
	WtKVCache		m_eidCache;
	//������ǻ�����
	WtKVCache		m_oidCache;

    PsiBaseDataMgr* mp_bdMgr; // �������ݹ�����
    PsiDataMgr*     mp_dataMgr; // ���ݹ�����
    PsiTraderDataMgr* mp_tdMgr; // �������ݹ�����
    PsiRedisDataMgr* mp_rdMgr; // Redis���ݹ�����

	PsiTraderTcpShmSpi* m_spi; // ���׽ӿڻص���

    bool m_bCheckTrade;    // �Ƿ��Ѿ���������ģ��

};

