/*!
 * \file PsiRunner.h
 * \project	PsiTraderMagicWeapon
 *
 * \author l<PERSON><PERSON>
 * \date 2024/02/20
 *
 * \brief 运行的主线程
 */
#pragma once

#include "PsiFasterDefs.h"
#include "PsiStruct.h"
#include "PsiVariant.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiDataMgr.h"
#include "PsiStraDataMgr.h"
#include "PsiSellStraDataMgr.h"
#include "PsiRedisDataMgr.h"
#include "PsiResourceMgr.h"
#include "TraderHuaX4.h"
#include "TraderHuaXCredit.h"
#include "PsiTimeLoader.h"
#include "PsiRedis.h"
// #include "PsiSubRunner.h"
#include "PsiSubTrader.h"
#include "PsiTcpShmConfCommon.h"
#include "PsiStraShareDataMgr.h"
#include "PsiStraTraderDataMgr.h"

class Server;

class
    PsiRunner {
public:
    PsiRunner();
    ~PsiRunner();

    /**
     * 读取配置文件
     * @return
     */
    bool config();

    /*
     *	初始化
     */
    bool init();

    /**
     * 数据绑定
     * @return
     */
    bool bind();

    /**
     * 运行Trader
     * @return
     */
    void runTraderWorker();
    /**
     * 运行Trader 服务器
     * @return
     */
    void runTraderServer();

    /**
     * 运行Runner
     */
     void runRunner();

    /**
        * 运行 缓存监听数据
        */
    bool runCacheMonitor();

    /**
     * 运行 交易缓存 监听数据
     */
    bool runTraderCacheMonitor();

    /**
     * 运行 交易监听数据
     * @return
     */
    bool runTraderMonitor();

    /**
     * 启动保存行情线程
     * @return
     */
    bool runSaveParser();
    /**
     * 运行
     */
    void run();

    /**
     * 处理缓存数据
     * @param cfg
     * @return
     */
    bool checkStrategyParams(const char* redisId, PsiVariant* cfg);

    /**
     * 处理缓存数据
     * @param cfg
     * @return
     */
    bool processTraderParams(const char* redisId, PsiVariant* cfg);

    bool doBuy(const char* redisId, PsiVariant* cfg);
    bool doSell(const char* redisId, PsiVariant* cfg);
    bool doCancel(const char* redisId, PsiVariant* cfg);

    bool doTrader(std::string clientId,std::string exchangeId,PSIDoTraderStruct& _doTrader);

    /**
     * 编辑股票基本信息
     * @param cfg
     * @return
     */
    bool editStockBaseInfo(const char* redisId, PsiVariant* cfg);

    /**
     * 更新账户信息
     * @param redisId
     * @param cfg
     * @return
     */
    bool updateTraderParams(const char* redisId, PsiVariant* cfg);

private:
    bool m_bRunning; // 运行状态

    PsiBaseDataMgr* mp_baseDataMgr; // 基础数据管理模块
    PsiDataMgr* mp_dataMgr; // 数据管理模块
    PsiResourceMgr* mp_resourceMgr; // 资源调度模块
    // psi_hashmap<std::string, PsiSubRunner*> m_RunnerMgrs; // 子Runner集合管理

    psi_hashmap<uint32_t, PsiStraDataMgr*> m_straDataMgrs; // 策略模块指针管理
    psi_hashmap<uint32_t, PsiSellStraDataMgr*> m_sellStraDataMgrs; // 卖出策略模块指针管理
    psi_hashmap<int, PsiStraTraderDataMgr*> m_straTraderDataMgrs; // 策略模块交易数据指针管理
    psi_hashmap<std::string, PsiStraShareDataMgr*> m_straShareDataMgrs; // 策略共享数据管理模块

//    psi_hashmap<std::string, TraderHuaX4*> m_traderMgrs; // 现货交易模块指针管理
//    psi_hashmap<std::string, TraderHuaXCredit*> m_traderCreditMgrs; // 信用交易模块指针管理

    psi_hashmap<std::string, PsiSubTrader*> m_subTraderMgrs; // 交易模块
    psi_hashmap<std::string, PsiTraderDataMgr*> m_subTraderDataMgrs; // 子账户交易数据指针管理

    psi_hashmap<std::string, PsiRedisDataMgr*> m_redisDataMgrs; // redis数据指针管理
    psi_hashmap<std::string, PsiRedis*> m_redisMgrs; // redis指针管理

    PsiVariant*	mp_config{}; // 配置文件

    tcpshm::ServerConf mp_tcpShmServerConf;//tcpshm服务端配置
    Server* m_tcpShmServer; //tcpshm服务端
    PsiTraderTcpShmSpi* mp_tcpShmSpi; //tcpshm服务端回调接口

    PsiTimeLoader* mp_timeLoader; // 时间加载器
    PsiVariant* mp_cfgLogger{}; // 日志配置
    StdThreadPtr m_threadWorker; // 主线程对象
    StdThreadPtr m_threadTraderWorker; // 主线程对象
    StdThreadPtr m_threadTraderMonitor; // 缓存监听线程对象
    StdThreadPtr m_threadSaveParser; // 保存行情线程
    int m_cacheCpuCore = 0; // 主线程绑定的CPU核心
    int m_traderCacheCpuCore = 0; // 交易监听线程绑定的CPU核心
    std::string m_contractRedisKey; // 合约信息redis key
    std::string m_contractRedisId; // 合约信息redis Id
    std::string  m_filePath;
//    PSIRedisRecMsgStruct m_redisRecMsgs[MAX_STRA_SIZE]; // redis接收消息
//    PSIRedisRecMsgStruct m_redisRecMsg;
//    PsiParserDataMgr* mp_parserMgr;
    // Boost.Asio 版本兼容: 新版 io_context + executor_work_guard；旧版 io_service + work
#if BOOST_VERSION >= 106600
    boost::asio::io_context m_asyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_work{m_asyncio.get_executor()};
#else
    boost::asio::io_service m_asyncio;
    std::unique_ptr<boost::asio::io_service::work> m_work;
#endif


    std::string m_loggerFilePath; // 日志文件路径
    std::string m_loggerFileHeader; // 日志文件头
    bool m_isShowConsole = false; // 是否显示控制台日志
};

