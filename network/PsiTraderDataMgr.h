/*!
 * \file PsiTraderDataMgr.cpp
 * \project	PsiTraderMagicWeapon
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 存储交易相关数据的管理类
 */
#pragma once
#include "PsiDataDef.hpp"
#include "PsiContractInfo.hpp"
#include "PsiVariant.hpp"
#include <boost/filesystem.hpp>
#include <boost/format.hpp>
#include "PsiMarcos.h"
#include "lockfree.hpp"
#include "TORATstpCreditUserApiStruct.h"
#include "TORATstpUserApiStruct.h"
#include "PsiStraTraderDataMgr.h"


class PsiTraderDataMgr {
public:
    PsiTraderDataMgr();
    ~PsiTraderDataMgr();

public:

    /**
     * 生成本地ID
     * @return
     */
    inline uint32_t	genLocalID(){
        return m_autoOrderId.fetch_add(1);
    }

    bool init();

    /**
     * 生成报单引用
     * @return
     */
    inline void	genEntrustID(char* buffer, uint32_t orderRef){
        sprintf(buffer, "%d", orderRef);
    }

    /**
     * 生成报单引用
     * @return
     */
    inline uint32_t getOrderRef(){
        return m_ordref.fetch_add(1);
    }

    /**
     * 生成用户标签
     * @param buffer
     * @param localid
     */
    inline bool makeUserTag(char* buffer, const char* orderPattern, uint32_t localid){
        boost::format fmt("%1%{}%2%");
        fmt % orderPattern % localid;
        strcpy(buffer, fmt.str().c_str());
        return true;
    }

    /**
     * 生成报单ID
     * @param buffer
     * @param length
     * @return
     */
    inline bool makeEntrustID(char* buffer, const char* user, const char* tradingday, int length){
        if (buffer == NULL || length == 0)
            return false;

        try
        {
            int orderref = m_ordref.fetch_add(1);
            boost::format fmt("%1%#%2%#%3%");
            fmt % user % tradingday % orderref;
            strcpy(buffer, fmt.str().c_str());
            return true;
        }
        catch (...)
        {

        }

        return false;
    }

    /**
     * 设置单笔交易上限
     * @param limit
     */
    inline void setSingleTraderUpperLimit(int limit){
        m_singleTraderUpperLimit = limit;
    }

    /**
     * 设置科创版单笔交易上限
     * @param limit
     */
    inline void setSingleTraderUpperLimit2(int limit){
        m_singleTraderUpperLimit2 = limit;
    }

    /**
     * 设置创业板单笔交易上限
     * @param limit
     */
    inline void setSingleTraderUpperLimit3(int limit){
        m_singleTraderUpperLimit3 = limit;
    }

    /**
     * 获取当前交易状态
     * @return
     */
    inline int getState(){
        return m_state;
    }

    /**
     * 设置账户类型
     */
    inline void setAccountType(const char* type) {
        m_accountType = type;
    }

    /**
     * 通过注册的redisID获取当前行情的所有参数
     * @param parserId
     * @return
     */
    TORACREDITAPI::CTORATstpBrokerCreditSecurityField& getBrokerCreditSecurity(const char* stdCode);

    /**
     * 设置redis模块的解析参数
     * @param parserId
     * @param params
     */
    void setBrokerCreditSecurity(const char* stdCode, TORACREDITAPI::CTORATstpBrokerCreditSecurityField params);
public:
    std::atomic<int>		m_ordref;		//报单引用
    std::atomic<uint32_t>		m_autoOrderId;
    lockfree::mpmc::Queue<PSIDoTraderStruct, 1024> m_traderQueue; // 计算线程资源调度队列
    lockfree::mpmc::Queue<PSIDoTraderQueryStruct*, 1024> m_queryQueue; // 查询队列
    std::string		m_user;
    std::string		m_pass;
    std::string		m_flowdir;
    bool			m_encrypt;  // 为网络数据是否加密传输，考虑数据安全性，建议以互联网方式接入的终端设置为加密传输
    std::string		m_authCode; // 终端名称
    std::string		m_appid; // 终端名称
    std::string		m_sseShareHoldId; // 沪市股东号
    std::string		m_szseShareHoldId; // 深市股东号
    std::string		m_terminal; // 终端类型，默认PC
    std::string		m_pubIp; // 公网IP
    std::string		m_pubPort; // 公网IP端口号
    std::string		m_tradeIp; // 内网交易IP
    std::string		m_mac; // MAC地址，托管服务器网卡MAC
    std::string		m_hardDisk; // 硬盘序列号,托管服务器硬盘序列号
    uint32_t        m_cpuCore=0; // 核心数
    uint32_t        m_tradeCpuCore=0; // 交易核心数
    uint32_t        m_queryCpuCore=0; // 查询核心数
    std::string		m_strBroker;
    std::string		m_front; // 如tcp://************:9500
    std::string		m_id;
    std::string		m_subId;
    std::string		m_orderPattern;
    std::string     m_redisId; // redis id
    std::string     m_redisChannel; // redis channel
    std::string     m_traderPushChannel; // 交易推送channel
    std::string     m_traderPositionPushChannel; // 交易推送channel
    std::string     m_traderPositionSetKey; // 持仓更新key
    std::string     m_traderOrderSetKey; // 下单更新key
    std::string     m_traderTransSetKey; // 成交更新key
    int             m_orderefIndex = ********;
    int             m_requestIndex = 1;

    bool			m_quick = false; // 流重传方式
    bool			m_inited = false;
    bool            m_proxy = false; // 是否需要代理
    std::string		m_accountType; // 账户类型
    psi_hashmap<std::string,int> m_codes;

    std::string		m_productInfo;
    std::string     m_exch;

    uint32_t		m_lDate;
    uint32_t		m_frontID;		//前置编号
    uint32_t		m_sessionID;	//会话编号
    std::string		m_strSettleInfo;

    psi_hashmap<std::string, TORASTOCKAPI::CTORATstpPositionField> m_positionMap; // 持仓信息
    psi_hashmap<std::string, TORACREDITAPI::CTORATstpPositionField> m_creditPositionMap; // 持仓信息

    psi_hashmap<int, PSIDoTraderOrderStruct> m_traderMap; // 账户交易信息

    psi_hashmap<int, PsiStraTraderDataMgr*> m_straTraderDataMgrs; // 策略模块交易数据指针管理

    TORACREDITAPI::CTORATstpInvestorRealTimeCreditInfoField m_investorRealTimeCreditInfo; // 投资者实时融资融券信息
    TORACREDITAPI::CTORATstpTradingAccountField m_tradingAccountCredit; // 融资融券资金账户信息
    TORASTOCKAPI::CTORATstpTradingAccountField m_tradingAccount; // 资金账户信息
    double m_totalMarketValue = 0; // 总市值
    double m_todayProfit = 0; // 今日盈亏
    typedef enum
    {
        TS_DISCONNECTED = 0,//未连接
        TS_CONNECTED,		//已连接
        TS_UNAUTHORIZED ,	//未认证
        TS_AUTHENTICATED,	//已认证
        TS_UNLOGIN,		    //未登录
        TS_LOGINED,			//已登录
        TS_CONFIRM_QRYED,   //结算已查
        TS_CONFIRMED,		//已确认
        TS_ORDERS_QRYED,	//订单已查
        TS_POSITION_QRYED,	//仓位已查
        TS_TRADES_QRYED,	//成交已查
        TS_ACCOUNT_QRYED,	//资金已查
        TS_ALLREADY,		//全部就绪
        TS_WAITING,			//等待中
        TS_ERROR,			//错误
    } TraderState; //只做初始检测用

    TraderState		m_state; // 交易状态

    int m_singleTraderUpperLimit = 100; // 单笔交易上限
    int m_singleTraderUpperLimit2 = 98600; // 科创版报单不能超过当前限制
    int m_singleTraderUpperLimit3 = 298600; // 创业板报单不能超过当前限制

    psi_hashset<std::string> m_setBrokerCreditSecurity;
    psi_hashmap<std::string, TORACREDITAPI::CTORATstpBrokerCreditSecurityField> m_mapBrokerCreditSecurity; // 存储券商信用证券信息
};


