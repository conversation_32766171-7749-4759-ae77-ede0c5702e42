#pragma once
#include <cstdint>
#include "PsiEndian.h"
#include <iostream>
#include<functional>
#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN
struct MsgHeader
{
    // size of this msg, including header itself
    // auto set by lib, can be read by user
    uint16_t size;
    // msg type of app msg is set by user and must not be 0
    uint16_t msg_type;
    // internally used for ptcp, must not be modified by user
    uint32_t ack_seq;

    template<bool ToLittle>
    void ConvertByteOrder() {
        Endian<ToLittle> ed;
        ed.ConvertInPlace(size);
        ed.ConvertInPlace(msg_type);
        ed.ConvertInPlace(ack_seq);
    }

    template<typename T>
    T* GetMsgBodyPtr() {
        return reinterpret_cast<T*>(this + 1);
    }

    char* GetMsgBodyRawPtr() {
        return reinterpret_cast<char*>(this) + sizeof(MsgHeader);
    }//少写一个<> ~

    template<class T>
    void SetMsg(std::function<void(T*)> fn) {
        msg_type = T::msg_type;
        T* msg = this->GetMsgBodyPtr<T>();
        fn(msg);
    }
};
NS_PSI_TCP_SHM_END
