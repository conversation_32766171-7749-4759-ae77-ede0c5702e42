#pragma once
#include <iostream>
#include <string>
#include <cstring>
#include <type_traits>
#include "PsiMsgHeader.h"
#include "PsiPtcpConn.h"
#include "PsiTcpShmConn.h"
#include "PsiTraderMsgType.h"

NS_PSI_TCP_SHM_BEGIN
template<class Conf>
std::string TcpShmConnection<Conf>::GetPtcpFile()
{
    return std::string(ptcp_dir_) + "/" + local_name_ + "_" + remote_name_ + ".ptcp";
}

template<class Conf>
bool TcpShmConnection<Conf>::IsClosed()
{
    std::lock_guard<std::recursive_mutex> lock(mutex_);
    return ptcp_conn_.IsClosed();
}

template<class Conf>
void TcpShmConnection<Conf>::Close()
{
    std::lock_guard<std::recursive_mutex> lock(mutex_);
    ptcp_conn_.RequestClose();
}
template<class Conf>
const char* TcpShmConnection<Conf>::GetCloseReason(int* sys_errno)
{
    return ptcp_conn_.GetCloseReason(sys_errno);
}
template<class Conf>
char* TcpShmConnection<Conf>::GetRemoteName()
{
    return remote_name_;
}
template<class Conf>
const char* TcpShmConnection<Conf>::GetLocalName()
{
    return local_name_;
}
template<class Conf>
const char* TcpShmConnection<Conf>::GetPtcpDir()
{
    return ptcp_dir_;
}
template<class Conf>
MsgHeader* TcpShmConnection<Conf>::Alloc(uint16_t size)
{
    if(shm_sendq_) return shm_sendq_->Alloc(size);
    return ptcp_conn_.Alloc(size);
}
template<class Conf>
void TcpShmConnection<Conf>::Push()
{
    if(shm_sendq_)
        shm_sendq_->Push();
    else
        ptcp_conn_.Push();
}
template<class Conf>
void TcpShmConnection<Conf>::PushMore()
{
    if(shm_sendq_)
        shm_sendq_->Push();
    else
        ptcp_conn_.PushMore();
}

// if caller dont call Pop() later, it will get the same msg again
// user dont need to call Front() directly as polling functions will do it
template<class Conf>
MsgHeader* TcpShmConnection<Conf>::Front()
{
    if(shm_recvq_) return shm_recvq_->Front();
    return ptcp_conn_.Front();
}
template<class Conf>
void TcpShmConnection<Conf>::Pop()
{
    if(shm_recvq_)
        shm_recvq_->Pop();
    else
        ptcp_conn_.Pop();
}
template<class Conf>
TcpShmConnection<Conf>::TcpShmConnection(const CommonConf& common_conf)
    :conf_(common_conf),ptcp_conn_(PTCPConnection<Conf>(common_conf))
{
    remote_name_[0] = 0;
}
template<class Conf>
void TcpShmConnection<Conf>::init(const char* ptcp_dir, const char* local_name)
{
    ptcp_dir_ = ptcp_dir;
    local_name_ = local_name;
}
template<class Conf>
bool TcpShmConnection<Conf>::OpenFile(bool use_shm, const char** error_msg)
{
    if(use_shm) {
        std::string shm_send_file = std::string("/") + local_name_ + "_" + remote_name_ + ".shm";
        std::string shm_recv_file = std::string("/") + remote_name_ + "_" + local_name_ + ".shm";
        std::cout<<"shm_send_file: "<<shm_send_file<<std::endl;
        if(!shm_sendq_) {
            shm_sendq_ = MyMmap<SHMQ>(shm_send_file.c_str(), true, error_msg);
            if(!shm_sendq_) return false;
        }
        if(!shm_recvq_) {
            shm_recvq_ = MyMmap<SHMQ>(shm_recv_file.c_str(), true, error_msg);
            if(!shm_recvq_) return false;
        }
        return true;
    }
    std::string ptcp_send_file = GetPtcpFile();
    return ptcp_conn_.OpenFile(ptcp_send_file.c_str(), error_msg);
}
template<class Conf>
bool TcpShmConnection<Conf>::GetSeq(uint32_t* local_ack_seq, uint32_t* local_seq_start, uint32_t* local_seq_end, const char** error_msg)
{
    if(shm_sendq_) return true;
    if(!ptcp_conn_.GetSeq(local_ack_seq, local_seq_start, local_seq_end)) {
        *error_msg = "Ptcp file corrupt";
        errno = 0;
        return false;
    }
    return true;
}
template<class Conf>
void TcpShmConnection<Conf>::Reset()
{
    if(shm_sendq_) {
        memset(shm_sendq_, 0, sizeof(SHMQ));
        memset(shm_recvq_, 0, sizeof(SHMQ));
    }
    else {
        ptcp_conn_.Reset();
    }
}
template<class Conf>
void TcpShmConnection<Conf>::Release()
{
    remote_name_[0] = 0;
    if(shm_sendq_) {
        MyUnmap<SHMQ>(shm_sendq_);
        shm_sendq_ = nullptr;
    }
    if(shm_recvq_) {
        MyUnmap<SHMQ>(shm_recvq_);
        shm_recvq_ = nullptr;
    }
    ptcp_conn_.Release();
}
template<class Conf>
void TcpShmConnection<Conf>::Open(int sock_fd, uint32_t remote_ack_seq, int64_t now)
{
    ptcp_conn_.Open(sock_fd, remote_ack_seq, now);
}
template<class Conf>
bool TcpShmConnection<Conf>::TryCloseFd()
{
    std::lock_guard<std::recursive_mutex> lock(mutex_);
    return ptcp_conn_.TryCloseFd();
}
template<class Conf>
MsgHeader* TcpShmConnection<Conf>::TcpFront(int64_t now)
{
    std::lock_guard<std::recursive_mutex> lock(mutex_);
    if (IsClosed()) return nullptr;
    ptcp_conn_.SendHB(now);
    return ptcp_conn_.Front(); // for shm, we need to recv HB and Front() always return nullptr
}
template<class Conf>
MsgHeader* TcpShmConnection<Conf>::ShmFront()
{
    return shm_recvq_->Front();
}

// template<class Conf>
// template<typename T>
// bool TcpShmConnection<Conf>::TrySendMsg(std::function<void(T*)> fn,bool is_client) {
//     MsgHeader* header = this->Alloc(sizeof(T));
//     if(!header) {
//         return false;
//     }
//     header->SetMsg<T>(fn);
//     // 这里的Msg2是冗余，但是除非构造新的function，否则无法 lambda->function 并且推导类型
//     if(is_client) {
//         this->Push();
//     }else {
//         this->Pop();
//         this->Push();
//     }

//     return true;
// }
template<class Conf>
template<typename T>
bool TcpShmConnection<Conf>::TrySendMsg(std::function<void(T*)> fn,bool is_client) {


    // 计算总大小 = MsgHeader + T结构体
    size_t total_size = sizeof(MsgHeader) + sizeof(T);

    // 分配足够的内存
    MsgHeader* header = this->Alloc(total_size);
    if (!header) {
        return false;
    }

    // 设置基础消息头
    header->msg_type = T::msg_type;  // 假设 T 有 msg_type 静态成员
    header->size = static_cast<uint16_t>(total_size);

    // 获取 T 结构体指针
    T* msg_ptr = header->GetMsgBodyPtr<T>();

    

    // 调用填充函数，传递结构体指针和额外数据指针
    fn(msg_ptr);

   

    if (is_client) {
        this->Push();
    } else {
        this->Pop();
        this->Push();
    }

    return true;
}

// template<class Conf>
// template<typename T>
// bool TcpShmConnection<Conf>::TrySendJsonMsg(std::function<void(T*)> fn, bool is_client, size_t extra_size) {
//     // 计算总大小 = 基础结构大小 + 额外数据大小
//     size_t total_size = sizeof(T) + extra_size;

//     // 分配足够的内存
//     MsgHeader* header = this->Alloc(total_size);
//     if(!header) {
//         return false;
//     }

//     // 设置消息头
//     header->SetMsg<T>(fn);  // 保留原有逻辑

//     if(is_client) {
//         this->Push();
//     } else {
//         this->Pop();
//         this->Push();
//     }

//     return true;
// }

template<class Conf>
template<typename T>
bool TcpShmConnection<Conf>::TrySendJsonMsg(std::function<void(T*, char*)> fn, bool is_client, size_t extra_size) {
    // 计算总大小 = MsgHeader + T结构体 + 额外数据大小
    size_t total_size = sizeof(MsgHeader) + sizeof(T) + extra_size;

    // 分配足够的内存
    MsgHeader* header = this->Alloc(total_size);
    if (!header) {
        return false;
    }

    // 设置基础消息头
    header->msg_type = T::msg_type;  // 假设 T 有 msg_type 静态成员
    header->size = static_cast<uint16_t>(total_size);

    // 获取 T 结构体指针
    T* msg_ptr = header->GetMsgBodyPtr<T>();

    // 计算额外数据区域的起始位置
    char* extra_data_ptr = reinterpret_cast<char*>(msg_ptr) + sizeof(T);

    // 调用填充函数，传递结构体指针和额外数据指针
    fn(msg_ptr, extra_data_ptr);

    if (is_client) {
        this->Push();
    } else {
        this->Pop();
        this->Push();
    }

    return true;
}

NS_PSI_TCP_SHM_END
