#pragma once
#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };
#include <stdint.h>  // C标准库头文件
#include <type_traits>
NS_PSI_TCP_SHM_BEGIN
template<bool ToLittle>
class Endian
{
public:
#if defined(__BYTE_ORDER__) && defined(__ORDER_LITTLE_ENDIAN__)
    static constexpr bool IsLittle = __BYTE_ORDER__ == __ORDER_LITTLE_ENDIAN__;
#elif defined(_WIN32) || defined(__INTEL__)
    static constexpr bool IsLittle = true;  // Windows/x86默认为小端
#else
    static constexpr bool IsLittle = false; // 其他情况默认大端
#endif
    template<typename T>
    static T Convert(T t) {
        if constexpr (ToLittle == IsLittle) return t;

        if constexpr (sizeof(T) == 2) {
#ifdef _MSC_VER
            return _byteswap_ushort(t);
#else
            return __builtin_bswap16(t);
#endif
        }
        else if constexpr (sizeof(T) == 4) {
#ifdef _MSC_VER
            return _byteswap_ulong(t);
#else
            return __builtin_bswap32(t);
#endif
        }
        else if constexpr (sizeof(T) == 8) {
#ifdef _MSC_VER
            return _byteswap_uint64(t);
#else
            return __builtin_bswap64(t);
#endif
        }
        else {
            return t;
        }
    }

    template<class T>
    void ConvertInPlace(T& t) {
        t = Endian<ToLittle>::Convert(t);
    }//!!! InPlace后缀
};
NS_PSI_TCP_SHM_END
