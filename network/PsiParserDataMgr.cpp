

#include "PsiParserDataMgr.h"


PsiParserDataMgr::PsiParserDataMgr()
{
    m_openBoardIndex = 0;
}

PsiParserDataMgr::~PsiParserDataMgr(){

}

/**
 * 初始化
 * @param psiBaseDataMgr
 * @return
 */
bool PsiParserDataMgr::init(PsiBaseDataMgr* psiBaseDataMgr, const char* parserId, const char* parserModel, const char* market, std::vector<std::string> stdCodes,
                            std::string subscribeType, bool flag){
    if(NULL ==  mp_baseDataMgr){
        mp_baseDataMgr = psiBaseDataMgr;
    }
    m_parserId = parserId;
    m_parserModel = parserModel;
    m_isSubscribeTranOrOrder = flag;
    m_stdCodes = stdCodes;
    m_subscribeType = subscribeType;
    m_marketId = market;
    return true;
}

/**
 * 设置当前行情订阅逐笔数据
 * @param flag
 */
void PsiParserDataMgr::setSubscribeTranOrOrder(bool flag){
    m_isSubscribeTranOrOrder = flag;
}

/**
 * 运行
 * @return
 */
bool PsiParserDataMgr::run(){
    return true;
}