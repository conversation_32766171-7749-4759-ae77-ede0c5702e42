/*!
 * \file PsiSubData.cpp
 * \project	PsiSubData
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 股票数据子类
 */
#pragma once

#include <cmath>
#include <vector>
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"
#include "PsiStraDataMgr.h"
#include "PsiSellStraDataMgr.h"
#include "PsiBaseDataMgr.h"
#include "PsiParserDataMgr.h"
#include "StdUtils.hpp"
#include "StrUtil.hpp"
#include "lockfree.hpp"
#include "PsiKlineDataInfo.hpp"
#include <cstdio>

class PsiSubData {
public:
    PsiSubData();
    ~PsiSubData();

    /**
     * @brief 初始化
     * @param pBaseDataMgr
     * @return
     */
    bool init(PsiBaseDataMgr *pBaseDataMgr, PsiTimeLoader *pTimeLoader);

    /**
     * 设置相关的数据存储
     * @param parserDataMgr
     * @return
     */
    bool setDataMgr(PsiParserDataMgr* parserDataMgr);

    /**
     * @brief 统计逐笔成交 和委托
     */
    void compute(int threadIndex); // 统计逐笔成交

    /**
     * 获取当前快速策略数量
     * @return
     */
    inline int getFastStraSize(){
        return m_fastStraSize;
    }

    /**
     * 设置快速线程数量
     * @param size
     */
    inline void setFastStraSize(int size){
        m_fastStraSize = size;
    }

    /**
     * 获取当前卖出策略数量
     * @return
     */
    inline int getSellStraSize(){
        return m_sellStraSize;
    }

    /**
     * 设置卖出线程数量
     * @param size
     */
    inline void setSellStraSize(int size){
        m_sellStraSize = size;
    }

    /**
     * @brief 初始化,代表一个code的不同时间划分的k线
     * @param strCode
     * @param strExchg
     * @param index
     * @param length
     * @param preClose
     * @param upPrice
     * @param downPrice
     */
    static inline PsiSubData* create(const char* strCode, const char* strName, const char* strExchg,
                                     const char* strPlate, int listingSector,int index, int length,
                                     int preClose, int iUpPrice, int downPrice, double dUpPrice,
                                     double dPreClose, double dDownPrice, bool isAlarmCode,
                                     int64_t volume10d, int64_t maxValidVolume, std::vector<int> klinePeriods, PsiSessionInfo* pSessionInfo){
        PsiSubData* pRet = new PsiSubData;
        pRet->m_index = index;
        pRet->m_strCode = strCode;
        pRet->m_strName = strName;
        pRet->m_strExchg = strExchg;
        pRet->m_listingSector = listingSector;
        pRet->m_strPlate = strPlate;
        pRet->m_length = length;
        pRet->m_iPreClose = preClose;
        pRet->m_iUpPrice = iUpPrice;
        pRet->m_dUpPrice = dUpPrice;
        pRet->m_downPrice = downPrice;
        pRet->m_dDownPrice = dDownPrice;
        pRet->m_dPreClose = dPreClose;
        pRet->m_alarmCode = isAlarmCode;
        pRet->m_marketStatus = 0;
        pRet->m_fastStraSize = 0;
        pRet->m_straSize = 0;
        pRet->m_volume10d = volume10d;
        pRet->m_maxValidVolume = maxValidVolume;
        pRet->m_maxTempVolume = 0;
        pRet->m_maxVolume = 0;
        pRet->m_volumeThres = 0;
        pRet->m_volumeThresAmount = 0.0;
        if(pRet->m_volume10d > 0){
            pRet->m_maxTempVolume = pRet->m_volume10d;
            if(pRet->m_maxValidVolume > 0 && pRet->m_maxValidVolume > pRet->m_maxTempVolume){
                pRet->m_maxTempVolume = pRet->m_maxValidVolume;
            }
        }
        pRet->m_l1Index = 0;
        pRet->m_l1ComputeIndex = 0;
        pRet->m_l1Details = new int[CODE_L1_LENGTHS];
        for(int i = 0; i < CODE_L1_LENGTHS; i++){
            pRet->m_l1Details[i] = -1;
        }
        pRet->m_sessionInfo = pSessionInfo;
        for(int i = 0; i < klinePeriods.size(); i++){
            // 填充时间
            std::vector<uint32_t> barMinutes = pSessionInfo->getPeriodMinList(klinePeriods[i]);
            PsiKlineDataInfo *pKlineInfo = PsiKlineDataInfo::create(strCode, barMinutes.size(), klinePeriods[i]);
            for(int k = 0; k < barMinutes.size(); k++){
                PSIBarStruct barStruct;
                memset(&barStruct, 0, sizeof(PSIBarStruct));
                barStruct.time = barMinutes[k];
                barStruct.tdate = pSessionInfo->getTraderDate();
                pKlineInfo->appendBar(barStruct);
            }
            pRet->m_codePeriodsKlineData.insert(std::make_pair(klinePeriods[i], pKlineInfo));
        }
        //这里只做初始化，runL1ParserDispatch会对其中的bar进行操作

        // printf("Create PsiSubData Success! Code:%s\n", pRet->m_strCode.c_str());
        fflush(stdout);
        return pRet;
    }

    /**
     * @brief 刷新基础信息
     * @param strCode
     * @param listingSector
     * @param index
     * @param length
     * @param preClose
     * @param iUpPrice
     * @param downPrice
     * @param dUpPrice
     * @param dPreClose
     */
    inline void refreshBaseInfo(std::string strCode, int index, int length, int preClose, int iUpPrice, int downPrice, double dUpPrice, double dPreClose){
        if(strCode == m_strCode && index == m_index){
            m_length = length;
            m_iPreClose = preClose;
            m_iUpPrice = iUpPrice;
            m_dUpPrice = dUpPrice;
            m_downPrice = downPrice;
            m_dPreClose = dPreClose;
            printf("[PsiSubData] Refresh Base Info Success! Code:%s m_length:%d m_iPreClose:%d m_iUpPrice:%d m_dUpPrice:%.2f m_downPrice:%d m_dPreClose:%.2f\n", m_strCode.c_str(), m_length, m_iPreClose, m_iUpPrice, m_dUpPrice, m_downPrice, m_dPreClose);
        }
    }
    /**
     * 清空数据
     * @return
     */
    inline void refreshData(){
        m_lastUpLimit = m_isUpLimit;
    }


public:
    int m_index = 0; // 存储股票索引

    std::string m_strCode; // 存储股票代码

    std::string m_strName; // 存储股票代码

    std::string m_strExchg; // 存储交易所

    PSIStockDirectionType m_directionType = PSI_STOCK_D_Buy; // 交易方向

    std::string m_strPlate; // 存储股票板块

    bool   m_alarmCode = false; // 是否已经预警的股票

    int m_length = 0; // 千档委托长度

    int m_iUpPrice = 0; // 涨停价乘以100后的整数

    double m_dUpPrice = 0; // 涨停价 double

    int m_downPrice = 0;// 跌停价乘以100后的整数

    double m_dDownPrice = 0;// 跌停价 double

    int m_lastPrice = 0; // 最新价乘以100后的整数

    int m_iPreClose = 0; // 存储昨收价乘以100后的整数

    double m_dPreClose = 0; // 存储昨收价

    int m_listingSector = 0; //上市板块 1:主板 2:科创版 3:创业板

    std::atomic<int> m_computeThread; // 当前票是否在计算线程进行计算（原子操作） -1表示未运行 不等于-1表示运行中

    std::atomic_flag m_computeLock = ATOMIC_FLAG_INIT; // 计算锁

    std::atomic<int> m_straThread;  // 当前票是否在策略线程进行计算（原子操作） -1表示未运行 不等于-1表示运行中

    std::atomic<int> m_bufferIndex; // 存储当前负责读取的缓存index

    PsiStraDataMgr* m_straDataMgrs[MAX_STRA_SIZE]; // 存储正常策略数据管理类指针

    PsiSellStraDataMgr* m_sellStraDataMgrs[MAX_STRA_SIZE]; // 存储正常卖出策略数据管理类指针

    PsiStraDataMgr* m_straFastDataMgrs[MAX_STRA_SIZE]; // 存储快速策略数据管理类指针

    psi_hashmap<int, PsiKlineDataInfo*> m_codePeriodsKlineData; // 一个code不同时间划分的K线

    int64_t m_actionTime = 0; // 逐笔数据刷新时间

    int64_t m_marketActionTime = 0; // 快照数据刷新时间

    int64_t m_lastMarketActionTime = 0; // 快照数据刷新时间

    PsiBaseDataMgr* mp_baseDataMgr; // 存储基础数据管理类指针

    PsiTimeLoader* mp_timeLoader; // 存储时间加载类指针

    PsiParserDataMgr* mp_parserDataMgr; // 行情数据存储

    bool m_isUpLimit = false;
    bool m_lastUpLimit = false;

    int m_firstUpLimitTime = 0;
    int m_openBoardCount = 0; // 存储开板数据数量
    int m_sealBoardCount = 0; // 存储封板数据数量
    int m_marketStatus = 0; // 存储市场状态 0表示正常 1表示封板 2表示开板
    int64_t m_lastActionTime = 0; // 记录上一次的存储时间

    psi_hashmap<std::string, int64_t> m_volumeMap; // 存储当前票对应交易Id的持仓信息

    int64_t m_volume10d;       // 10日成交量
    int64_t m_maxValidVolume;       // 100天内压力位附近最大成交量
    int64_t m_maxTempVolume;  // 临时最大成交量
    int64_t m_maxVolume;    //最大成交量
    int64_t m_volumeThres; // 成交量阈值
    double m_volumeThresAmount; // 成交量阈值

    int* m_l1Details; // 存储L1数据
    int m_l1Index = 0; // 存储L1数据索引

    PsiSessionInfo* m_sessionInfo; // 存储当前会话信息
private:
    int m_fastStraSize = 0; // 存储快速策略数量
    int m_straSize = 0; // 存储策略数量
    int m_sellStraSize = 0; //卖出策略数量

    int m_l1ComputeIndex = 0; // L1数据计算位置
};

