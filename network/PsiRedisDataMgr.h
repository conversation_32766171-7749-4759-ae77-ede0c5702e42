/*!
 * \file PsiRedisDataMgr.cpp
 * \project	PsiRedisDataMgr
 *
* \author liji<PERSON>
* \date 2024/03/07
 *
 * \brief 存储缓存相关的数据
 */
#pragma once
#include <string>
#include "lockfree.hpp"
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"



class PsiRedisDataMgr {
public:
    PsiRedisDataMgr();
    ~PsiRedisDataMgr();
    /**
     * 获取redis id
     * @return
     */
    std::string getRedisId() const;

    /**
     * 设置redis id
     * @param redisId
     */
    void setRedisId(const char* redisId);
public:

    lockfree::spsc::RingBuf<PSIRedisRecMsgStruct, 1024U> m_readRingBuffer; // 环形缓冲区

    lockfree::spsc::Queue<PSIRedisPushMsgStruct, 1024U> m_writeQueue; // 环形缓冲区

    lockfree::mpmc::Queue<PSIDoTraderOrderStruct, 1024U> m_traderQueue; // 环形缓冲区

    lockfree::mpmc::Queue<PSIRedisPushMsgStruct, 1024U> m_traderInfoQueue; // 环形缓冲区

    lockfree::spsc::RingBuf<PSIRedisRecMsgStruct, 1024U> m_readTraderRingBuffer; // 环形缓冲区

private:
    std::string m_redisId; // redis id
};

