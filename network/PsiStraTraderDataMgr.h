/*!
 * \file PsiShareDataMgr.cpp
 * \project	PsiStraTraderDataMgr
 *
* \author liji<PERSON>
* \date 2024/03/07
 *
 * \brief 存储当前每个策略每只股票的下单数据
 */
#pragma once
#include <string>
#include <vector>
#include "lockfree.hpp"
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"
class PsiStraTraderDataMgr {
public:
    PsiStraTraderDataMgr();
    ~PsiStraTraderDataMgr();

    void init(uint32_t straId, const std::string& strCode, const std::string& strExchg);
public:
    std::map<int, PSIDoTraderOrderStruct> m_doTraderMap; // 存储当前每个策略每只股票的下单数据
private:
    uint32_t	m_straId; // 策略ID
    std::string m_strCode; // 存储股票代码

    std::string m_strExchg; // 存储交易所
};

