#pragma once
#include "PsiMarcos.h"
#include "PsiStruct.h"
#include "TORATstpCreditUserApiStruct.h"
#include "TORATstpUserApiStruct.h"

#include <string>

class PsiVariant;



class PsiCfgLoader
{
	static PsiVariant*	load_from_json(const char* content);
	static PsiVariant*	load_from_yaml(const char* content);

public:
	static PsiVariant*	load_from_file(const char* filename);
	static PsiVariant*	load_from_content(const std::string& content, bool isYaml = false);

	static PsiVariant*	load_from_file(const std::string& filename)
	{
		return load_from_file(filename.c_str());
	}
    /**
     * д��JSON
     * @param json
     * @return
     */
    static std::string writeTraderStatusJson(int status, const char* traderId, const char* msg);

    /**
     * д��JSON
     * @param json
     * @return
     */
    static std::string writeTraderAccountJson(double usefullMoney, double totalMarketValue, double todayProfit, uint32_t accountDate, const char* traderId, const char* accountType, int status, const char* msg);

    /**
     * д��JSON
     * @param pInvestorRealTimeCreditInfo
     * @param accountDate
     * @param traderId
     * @param accountType
     * @return
     */
    static std::string writeCreditTraderAccountJson(TORACREDITAPI::CTORATstpInvestorRealTimeCreditInfoField* pInvestorRealTimeCreditInfo,uint32_t accountDate, const char* traderId, const char* accountType, int status, const char* msg);

    /**
     * �ӽṹ������JSON�ַ���
     * @param person
     * @return
     */
    static std::string structToJson(const PSIDoTraderOrderStruct& person);

    /**
     * ���ֲ���Ϣת����JSON
     * @param positionField
     * @param accountDate
     * @param traderId
     * @param accountType
     * @param status
     * @param msg
     * @return
     */
    static std::string positionStructToJson(TORASTOCKAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId);

    /**
     * ���ֲ���Ϣת����JSON
     * @param positionField
     * @param accountDate
     * @param traderId
     * @param accountType
     * @param status
     * @param msg
     * @return
     */
    static std::string creditPositionStructToJson(TORACREDITAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId);

    /**
     * ���ֲ���Ϣת����JSON
     */
    static std::string positionStructToJsonX(TORASTOCKAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId);


    static std::string tradeStructToJson(TORASTOCKAPI::CTORATstpTradeField pTradeField, uint32_t accountDate, const char* traderId);

    static std::string orderStructToJson(TORASTOCKAPI::CTORATstpOrderField pOrderField, uint32_t accountDate, const char* traderId);

    /**
* ���ֲ���Ϣת����JSON Credit
* @param positionField
* @param accountDate
* @param traderId
* @return
*/
    static std::string CreditPositionStructToJson(TORACREDITAPI::CTORATstpPositionField positionField, uint32_t accountDate, const char* traderId);


    /**
     * ���ɽ���Ϣת����JSON Credit
     * @param pTradeField
     * @param accountDate
     * @param traderId
     * @return
     */
    static std::string CreditTradeStructToJson(TORACREDITAPI::CTORATstpTradeField pTradeField, uint32_t accountDate, const char* traderId);


    /**
     * ��������Ϣת����JSON  Credit
     * @param pTradeField
     * @param accountDate
     * @param traderId
     * @return
     */
    static std::string CreditOrderStructToJson(TORACREDITAPI::CTORATstpOrderField pOrderField, uint32_t accountDate, const char* traderId);
};

