/*!
 * \file PsiSubRunner.h
 * \project	PsiTraderMagicWeapon
 *
 * \author l<PERSON><PERSON>
 * \date 2024/02/20
 *
 * \brief 运行的主线程 子线程
 */
#pragma once

#include <boost/asio.hpp>
// Boost.Asio 版本兼容性：老版本无 executor_work_guard
#if BOOST_VERSION >= 106600
#include <boost/asio/executor_work_guard.hpp>
#else
#include <boost/asio/io_service.hpp>
#endif
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"
#include "PsiVariant.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiParserDataMgr.h"
#include "PsiDataMgr.h"
#include "PsiResourceMgr.h"
#include "ParserHuaXL2.h"
#include "ParserHuaXL1.h"
#include "PsiTimeLoader.h"
#include "PsiSubParser.h"

#include <regex>


class PsiSubRunner {
public:
    PsiSubRunner();
    ~PsiSubRunner();

    /*
     *	初始化
     */
    bool init(PsiBaseDataMgr* pBaseDataMgr, PsiDataMgr* pDataMgr, PsiResourceMgr* pResourceMgr, PsiTimeLoader* psiTimeLoader, PSIRunnerConfigStruct runnerConfig, psi_hashmap<std::string, PsiTraderDataMgr*> &psiTraderDataMgrs, const char* runnerId);

    /**
     * 运行
     */
    void run();

    bool runL1ParserDispatch();

    void computeBoard(TORALEV1API::CTORATstpMarketDataField lev1MarketDataField, int _codeIndex);

    inline int fast_atoi( const char * str )
    {
        int val = 0;
        while( *str ) {
            val = val*10 + (*str++ - '0');
        }
        return val;
    }
public:
    uint32_t	m_runnerIndex; // 运行索引
    std::string m_runnerId; // 运行ID
    PsiBaseDataMgr* mp_baseDataMgr; // 基础数据管理模块
    PsiDataMgr* mp_dataMgr; // 数据管理模块
    PsiResourceMgr* mp_resourceMgr; // 资源调度模块
    PsiTimeLoader* mp_timeLoader; // 时间加载器
    // std::string m_parserId; // 行情模块ID
    std::vector<std::string> m_parsers; // 行情列表
    psi_hashmap<std::string, PsiSubParser*> m_parserMgrs; // 行情指针
    psi_hashmap<std::string, PsiParserDataMgr*> m_parserDataMgrs; // 行情数据指针列表

    PsiParserDataMgr* m_marketDataParserData; // 快照行情数据指针
    PsiSubParser*     m_strokeParser;
    PsiParserDataMgr* m_strokeParserData; // 逐笔行情数据指针
//    ParserHuaXL2* mp_parser; // 行情模块指针管理
//    PsiParserDataMgr* mp_parserDataMgr; // 行情数据管理
    std::vector<int> m_computeCpuCores; // 计算模块CPU核心列表
    std::vector<int> m_straCpuCores; // 策略模块CPU核心列表
    std::vector<int> m_listingSectorProportion; // 策略模块CPU核心列表
    // std::vector<std::string> m_stdCodes; // 标的代码列表
    int m_parserDispatchCpuCore = 0;
    int m_parserL2DispatchCpuCore = 0;
    int m_parserL1DispatchCpuCore = 0;
    int m_subscribeCpuCore = 0;
    StdThreadPtr m_worker;
    StdThreadPtr m_l2Worker;
    StdThreadPtr m_l1Worker;
    StdThreadPtr m_subscribeWorker;
    int64_t m_orderTotalTime = 0;
    int64_t m_orderRecTotalTime = 0;
    int m_orderTimeIndex = 0;
    int m_orderRecTimeIndex = 0;

    int64_t m_transTotalTime = 0;
    int64_t m_transRecTotalTime = 0;
    int m_transTimeIndex = 0;
    int m_transRecTimeIndex = 0;

    int64_t m_marketTotalTime = 0;
    int m_marketTimeIndex = 0;

    CodeSet m_subscribeCodes; // 订阅列表
    int m_maxPriceProportion = 550;

#if BOOST_VERSION >= 106600
    boost::asio::io_context m_subscribeAsyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_subscribeWork{m_subscribeAsyncio.get_executor()};
#else
    boost::asio::io_service m_subscribeAsyncio;
    std::unique_ptr<boost::asio::io_service::work> m_subscribeWork;
#endif

    std::atomic_flag m_l1SwapLock = ATOMIC_FLAG_INIT;
    std::atomic_flag m_l2SwapLock = ATOMIC_FLAG_INIT; // 快照数据加锁
    std::atomic_flag m_strokeSwapLock = ATOMIC_FLAG_INIT; // 逐笔数据加锁

    // int m_startTime = 92500000;
    // int m_endTime = 151500000;
    psi_hashmap<std::string,int> m_codes; // 股票列表
};

