
#include "PsiTimeLoader.h"
#include "TimeUtils.hpp"

PsiTimeLoader::PsiTimeLoader()
    : m_sysTime(0)
    , m_curTime(0)
    , m_cpuCore(0)
{
}

PsiTimeLoader::~PsiTimeLoader()
{
}

bool PsiTimeLoader::init(const int cpuCore)
{
    m_cpuCore = cpuCore;
    return true;
}

bool PsiTimeLoader::run()
{
    // 启动线程 绑定核心
    if (m_threadWorker == NULL)
    {
        m_threadWorker.reset(new StdThread([this]() {
#ifdef _WIN32
            if (m_cpuCore > 0) {
                DWORD_PTR mask = 1ULL << m_cpuCore;
                DWORD_PTR ret = SetThreadAffinityMask(GetCurrentThread(), mask);
                if (ret == 0) {
                    DWORD err = GetLastError();
                    printf("PsiTimeLoader::run SetThreadAffinityMask failed, error:%lu\n", err);
                } else {
                    printf("PsiTimeLoader::run SetThreadAffinityMask success cpu_core:%d\n", m_cpuCore);
                }
            }
#else
            (void)m_cpuCore; // Linux/Unix: 暂不绑定 CPU 亲和性，可用 pthread_setaffinity_np 实现
#endif
            m_sysTime = TimeUtils::getCurNanoTime();
            m_initTime = std::chrono::high_resolution_clock::now();
            printf("PsiTimeLoader::run m_cpuCore:%d m_sysTime:%lld m_initTime:%lld\n ", m_cpuCore, m_sysTime, m_initTime);

            while (true) {
                m_curTime.store(std::chrono::duration_cast<std::chrono::nanoseconds>(std::chrono::high_resolution_clock::now() - m_initTime).count());
                // m_curNowTime.store(TimeUtils::getCurNanoTime());
                // printf("PsiTimeLoader::run m_curTime:%ld nowTime:%ld m_sysTime:%lld\n", m_curTime.load(), m_curNowTime.load(), m_sysTime);
            }
        }));
    }
    return true;
}

