/*!
 * \file ParserHuaX.h
 * \project	PsiTraderMagicWeapon
 *
 * \author HeJ
 * \date 2022/08/10
 *
 * \brief
 */
#pragma once
#include "TORATstpLev2MdApi.h"
#include <map>

#include "PsiDataDef.hpp"
#include "PsiContractInfo.hpp"
#include "PsiVariant.hpp"
#include "StrUtil.hpp"
#include "TimeUtils.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiDataMgr.h"
#include "PsiResourceMgr.h"
#include "PsiParserDataMgr.h"

#include <boost/filesystem.hpp>
#include <boost/asio.hpp>
// 函数: Boost.Asio 版本兼容性处理
// 作用: Ubuntu 18.04的Boost版本较老，可能没有executor_work_guard.hpp
#if BOOST_VERSION >= 106600
#include <boost/asio/executor_work_guard.hpp>
#else
// 使用旧版本的 io_service::work 替代
#include <boost/asio/io_service.hpp>
#endif
#include "PsiMarcos.h"

#define _CRT_SECURE_NO_WARNINGS
#define CONNECT_MARKET_SH	"USH"			//�Ϻ��г�
#define CONNECT_MARKET_SZ	"USZ"			//�����г�
#define CONNECT_MARKET_SHSZ	"USH;USZ"		//�Ϻ��������г�

#define _TORA_TSTP_MST_TCP = "0";
///UDP����ģʽ
#define _TORA_TSTP_MST_UDP = "1";
///UDP�鲥ģʽ
#define _TORA_TSTP_MST_MCAST = "2";
///Ԥ��
#define _TORA_TSTP_MST_DMA = '3';
///PROXYģʽ
#define _TORA_TSTP_MST_PROXY = "4";

class PSISummaryData;
class PSIThousandLevelOrdersInfo;

using namespace TORALEV2API;

typedef CTORATstpLev2MdSpi HuaXParserL2Spi;
typedef CTORATstpLev2MdApi HuaXParserL2Api;


class ParserHuaXL2 : public HuaXParserL2Spi
{
public:
    ParserHuaXL2();
	~ParserHuaXL2();

public:
	enum LoginStatus
	{
		LS_NOTLOGIN,
		LS_LOGINING,
		LS_LOGINED
	};

public:
    /**
     * ��ʼ��
     * @param config
     * @return
     */
	bool init() ;

    /**
     * �ͷ�
     */
	void release();

    /**
     * ����
     * @return
     */
	bool connect();

    /**
     * ����
     * @return
     */
    bool run();

    /**
     * �Ͽ�����
     * @return
     */
	bool disconnect();

    /**
     * �Ƿ�����
     * @return
     */
	bool isConnected();

    /**
     * ����
     * @param vecSymbols
     */
	void subscribe(const CodeSet &vecSymbols);

    /**
     * ȡ������
     * @param vecSymbols
     */
	void unsubscribe(const CodeSet &vecSymbols);

	void registerSpi(PsiBaseDataMgr *baseDataMgr, PsiDataMgr *dataMgr, PsiParserDataMgr *parserDataMgr, const char* parserId, int computeCoreSize);


//CTORATstpLev2MdSpi �ӿ�
public:
	virtual void OnFrontConnected() override;

	///���ͻ����뽻�׺�̨ͨ�����ӶϿ�ʱ���÷��������á���������������API���Զ��������ӣ��ͻ��˿ɲ���������
	///        -3 �����ѶϿ�
	///        -4 �����ʧ��
	///        -5 ����дʧ��
	///        -6 ����������
	///        -7 ����Ŵ���
	///        -8 �������������
	///        -9 ����ı���
	virtual void OnFrontDisconnected(int nReason) override;

    // ����Ӧ��
    virtual void OnRspError(CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    // ��¼������Ӧ
    virtual void OnRspUserLogin(CTORATstpRspUserLoginField *pRspUserLogin, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    // �ǳ�������Ӧ
    virtual void OnRspUserLogout(CTORATstpUserLogoutField *pUserLogout, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    //���Ŀ�������Ӧ��
    virtual void OnRspSubMarketData(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    // ȡ����������Ӧ��
    virtual void OnRspUnSubMarketData(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    // ����ָ������Ӧ��
    virtual void OnRspSubIndex(TORALEV2API::CTORATstpSpecificSecurityField* pSpecificSecurity, TORALEV2API::CTORATstpRspInfoField* pRspInfo, int nRequestID, bool bIsLast) override;

    // ������ʳɽ�����Ӧ��
    virtual void OnRspSubTransaction(CTORATstpSpecificSecurityField* pSpecificSecurity, CTORATstpRspInfoField* pRspInfo, int nRequestID, bool bIsLast) override;

    ///ȡ��������ʳɽ�Ӧ��(���ڷ�ծȯ�ࡢ���ڿ�תծ)
    virtual void OnRspUnSubTransaction(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    // �������ί������Ӧ��
    virtual void OnRspSubOrderDetail(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    ///ȡ���������ί��Ӧ��(���ڷ�ծȯ�ࡢ���ڿ�תծ)
    virtual void OnRspUnSubOrderDetail(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    // ������ծ�������Ӧ��
    virtual void OnRspSubXTSTick(CTORATstpSpecificSecurityField* pSpecificSecurity, CTORATstpRspInfoField* pRspInfo, int nRequestID, bool bIsLast) override;

    // �����Ϻ�XTSծȯ����Ӧ��
    virtual void OnRspSubXTSMarketData(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    ///�����Ϻ�NGTS��ծȯ�������Ӧ��
    virtual void OnRspSubNGTSTick(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;

    ///ȡ�������Ϻ�NGTS��ծȯ�������Ӧ��
    virtual void OnRspUnSubNGTSTick(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast) override;


    /***********************************�ص���������***********************************/

    // ��������֪ͨ
    virtual void OnRtnMarketData(CTORATstpLev2MarketDataField *pDepthMarketData, const int FirstLevelBuyNum, const int FirstLevelBuyOrderVolumes[], const int FirstLevelSellNum, const int FirstLevelSellOrderVolumes[]) override;

    // ָ����������֪ͨ
    virtual void OnRtnIndex(CTORATstpLev2IndexField* pIndex) override;

    //��ʳɽ�֪ͨ
    virtual void OnRtnTransaction(CTORATstpLev2TransactionField *pTransaction) override;

    //���ί��֪ͨ
    virtual void OnRtnOrderDetail(CTORATstpLev2OrderDetailField * pOrderDetail) override;

    ///�Ϻ�NGTS��ծȯ�������֪ͨ
    virtual void OnRtnNGTSTick(CTORATstpLev2NGTSTickField *pTick) override;

    //�Ϻ�XTSծȯ��������֪ͨ
    virtual void OnRtnXTSMarketData(CTORATstpLev2XTSMarketDataField *pMarketData,
                                    const int FirstLevelBuyNum, const int FirstLevelBuyOrderVolumes[],
                                    const int FirstLevelSellNum, const int FirstLevelSellOrderVolumes[]) override;

    //�Ϻ�XTSծȯ�������֪ͨ
    virtual void OnRtnXTSTick(CTORATstpLev2XTSTickField *pTick) override;

    /**
     * ������ʳɽ������ί��
     * @param ppSecurityID
     * @param nCount
     * @param ExchageID
     */
    int subscribeTO(char *ppSecurityID[], int nCount, TTORATstpExchangeIDType ExchageID);

    /**
     * ������ʳɽ������ί��
     * @param ppSecurityID
     * @param nCount
     * @param ExchageID
     */
    int unSubscribeTO(char *ppSecurityID[], int nCount, TTORATstpExchangeIDType ExchageID);

private:
	/*
	 *	���͵�¼����
	 */
	void DoLogin();
	/*
	 *	����Ʒ������
	 */
	void DoSubscribeMD();

    /**
     * ���ж���
     * @param ppSecurityID
     * @param nCount
     * @param ExchageID
     */
    void doSubscribe(char *ppSecurityID[], int nCount, TTORATstpExchangeIDType ExchageID);

    /**
     * ��ȡ��Լ��
     * @param code
     * @return
     */
    std::string getContractCode(const char* code);

private:
	uint32_t			_uTradingDate;
	LoginStatus			m_loginState;
    HuaXParserL2Api*		mp_api;
	std::string			m_subMode; // TCP����ģʽ 0  UDP����ģʽ 1 UDP�鲥ģʽ 2
	bool			m_cachedMode; // ���칦������ģʽ
	std::string			m_front;  // tcp://************:9402
    std::string         m_interfaceIp; // ����������ַ,��:"127.0.0.1",�� NULL ��������ѯ���Ա����������������鲥��
    std::string         m_sourceIp; // �鲥���ݰ�Դ��ַ,��:"127.0.0.1",�� NULL ��ʾ��У�����ݰ�Դ
    std::string			m_market; // USH USZ USH;USZ
    std::string         m_codes; // ��ǰ���鶩��code
	std::string			m_strUser;
	std::string			m_strPass;
	std::string			m_strFlowDir;
    std::string         m_subscribeType; // �ն�����
    bool                m_proxy; // �Ƿ���Ҫ����
    std::string         m_cpuCores; // CPU����
    int                 m_orderCpuCore = 0; // ί���̺߳�
    int                 m_transCpuCore = 0; // �ɽ��̺߳�
    std::string         m_interfaceName; // ����������
    int                 m_rxqCapacity = 0; // ������������
    bool                m_befvi; // �Ƿ�������������
    bool                m_isConnected = false;

	CodeSet				m_fitSHSubs;
	CodeSet				m_fitSZSubs;
	int					m_iRequestID;
    std::string         m_parserId; // ������ID

    uint32_t		    m_gpsize;
    PsiBaseDataMgr*      mp_baseDataMgr; // �������ݹ�����
    PsiDataMgr*          mp_dataMgr; // ���ݹ�����
    PsiParserDataMgr*    mp_parserDataMgr; // �������ݹ���
    int                  m_computeCoreSize = 0; // ���������
    std::atomic<int> m_computeThreadCore; //���һ�εļ����̵߳�index
    psi_hashmap<std::string, std::string> _mapContracts;
    int                 m_startTime = 92500000;
    int                 m_endTime = 150000000;

    StdThreadPtr				m_orderWorker;
    StdThreadPtr				m_transWorker;
    // 函数: Boost.Asio 版本兼容的异步IO上下文
    // 作用: 新版本使用io_context，老版本使用io_service
#if BOOST_VERSION >= 106600
    boost::asio::io_context m_asyncio;
    boost::asio::executor_work_guard<boost::asio::io_context::executor_type> m_work{m_asyncio.get_executor()};
#else
    boost::asio::io_service m_asyncio;
    std::unique_ptr<boost::asio::io_service::work> m_work;
#endif

    int64_t m_orderTotalTime = 0;
    int64_t m_orderRecTotalTime = 0;
    int m_orderTimeIndex = 0;
    int m_orderRecTimeIndex = 0;

    int64_t m_transTotalTime = 0;
    int64_t m_transRecTotalTime = 0;
    int m_transTimeIndex = 0;
    int m_transRecTimeIndex = 0;
    CTORATstpLev2TransactionField transactionField;
    CTORATstpLev2OrderDetailField orderDetailField;
};

//��ȡIDataMgr�ĺ���ָ������
typedef ParserHuaXL2* (*FuncCreateParser)();
typedef void(*FuncDeleteParser)(ParserHuaXL2* &parser);
