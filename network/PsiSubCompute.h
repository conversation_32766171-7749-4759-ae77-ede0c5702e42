/*!
 * \file PsiSubCompute.cpp
 * \project	PsiSubCompute
 *
* \author l<PERSON><PERSON>
* \date 2024/02/05
 *
 * \brief 计算线程子类
 */
#pragma once
#include "PsiTypes.h"
#include "lockfree.hpp"
#include "PsiTypes.h"
#include "StdUtils.hpp"
#include "PsiDataMgr.h"
#include "PsiResourceMgr.h"


class PsiResourceMgr;
class PsiSubCompute {
public:
    PsiSubCompute();
    ~PsiSubCompute();

    /**
     * @brief 初始化
     * @param cpuCore
     * @return
     */
    bool init(PsiDataMgr* dataMgr, PsiResourceMgr* resourceMgr, int cpuCore, int straThreadSize=0);

    /**
     * @brief 启动
     * @return
     */
    bool run();
public:
    lockfree::spsc::RingBuf<int, 2048U> m_threadQueue; // 计算线程队列
    std::atomic<int> m_threadStatus; // 当前线程状态 -1表示未运行 不等于-1表示运行中
    uint32_t	m_computeThreadId; // 计算线程ID
    int m_cpuCore = 0; // 计算线程绑定的CPU核心
    PsiDataMgr* mp_dataMgr; // 股票数据管理模块
    StdThreadPtr m_threadWorker; // 线程对象
    int m_straThreadSize = 0; // 策略线程数量
    int m_currentIndex = 0; // 当前计算线程下标
    std::atomic_flag m_swapLock = ATOMIC_FLAG_INIT; // 数据加锁
private:
    PsiResourceMgr* mp_resourceMgr; // 资源调度模块
    int* m_codeIndex; // 股票代码下标
};


