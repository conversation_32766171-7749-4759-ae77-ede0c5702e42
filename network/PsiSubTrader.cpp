//
// Created by Administrator on 2024/6/25.
//

#include "PsiSubTrader.h"
#include <memory>
#ifdef _WIN32
#include <windows.h>
#endif


PsiSubTrader::PsiSubTrader()
    : m_pTraderCTP(NULL)
    , mp_baseDataMgr(NULL)
    , mp_dataMgr(NULL)
    , mp_tdMgr(NULL)
    , mp_rdMgr(NULL){

}

PsiSubTrader::~PsiSubTrader(){

}

/**
 *  ��ʼ��
 * @param pBaseDataMgr
 * @param pDataMgr
 * @param traderId
 * @return
 */
bool PsiSubTrader::init(PsiBaseDataMgr* pBaseDataMgr, PsiDataMgr* pDataMgr,
                        PsiTraderDataMgr* psiTraderDataMgr, PsiRedisDataMgr* psiRedisDataMgr,
                        const char* traderId, const char* subTraderId, const char* traderModule,PsiTraderTcpShmSpi* m_pTraderTcpShmSpi){
    mp_baseDataMgr = pBaseDataMgr;
    mp_dataMgr = pDataMgr;
    mp_tdMgr = psiTraderDataMgr;
    mp_rdMgr = psiRedisDataMgr;
    m_traderId = traderId;
    m_subTraderId = subTraderId;
    m_traderModule = traderModule;
    // ��ʼ������
    if(m_traderModule == CTP_TRADER){
        m_pTraderCTP = new TraderCTP();
        m_pTraderCTP->registerSpi(mp_baseDataMgr, mp_dataMgr, psiTraderDataMgr, psiRedisDataMgr);
        m_pTraderCTP->registerTraderSpi(m_pTraderTcpShmSpi);
        m_pTraderCTP->init();
    } else if(m_traderModule == HUAX_CREDIT_TRADER){
        // logw("[PsiSubTrader] Trader Not Support [{}]", m_traderModule.c_str());
    }
    return true;
}

/**
 * ����
 * @return
 */
bool PsiSubTrader::run(){
    // logi("[PsiSubTrader] Trader {} start! m_traderId:{}", m_traderModule.c_str(), m_traderId.c_str());
    // int i=0;
    if(m_worker == NULL){
        m_worker = std::make_shared<StdThread>([this](){
            // logi("i:{}",i++);
#ifdef _WIN32
            if(mp_tdMgr->m_queryCpuCore > 0) {
                DWORD_PTR mask = 1ULL << mp_tdMgr->m_queryCpuCore;
                DWORD_PTR ret = SetThreadAffinityMask(GetCurrentThread(), mask);
                if (ret == 0) {
                    // loge("[PsiSubTrader] SetThreadAffinityMask failed! Error:{}", GetLastError());
                } else {
                    // logi("[PsiSubTrader] Thread affinity success! CPU core:{}", mp_tdMgr->m_queryCpuCore);
                }
            }
#else
            (void)mp_tdMgr->m_queryCpuCore; // Linux: 暂不设置线程亲和性
#endif
            if(m_traderModule == CTP_TRADER){
                  if(m_pTraderCTP->connect()!=true) {
                      // loge("[PsiSubTrader] Trader {} connect failed!", m_traderModule.c_str()); //�״�����ʧ�ܣ�Ӧ������
                  }
            } else if(m_traderModule == HUAX_CREDIT_TRADER){
                // logw("[PsiSubTrader] Trader Not Support [{}]", m_traderModule.c_str());
            }
            PSIDoTraderStruct orderStruct;
            memset(&orderStruct, 0, sizeof(PSIDoTraderStruct));
            while (true)
            {
                while(mp_tdMgr->m_traderQueue.Pop(orderStruct)) {
                    // logi("[PsiSubTrader] Trader {} ReqId: {}", m_traderModule.c_str(), orderStruct.request_id);
                    if(orderStruct.direction_type == '0' || orderStruct.direction_type == '1')
                    {
                        m_pTraderCTP->orderInsert(orderStruct);
                    }
                    else
                    {
                        m_pTraderCTP->orderAction(orderStruct);
                    }
                }
            }
        });
    }
    return true;
}

