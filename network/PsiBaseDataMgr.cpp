/*!
 * \file PsiBaseDataMgr.cpp
 * \project	PsiTraderMagicWeapon
 *
* \author liji<PERSON>
* \date 2024/02/05
 *
 * \brief
 */

#include "PsiBaseDataMgr.h"
#include "StdUtils.hpp"
#include "PsiCfgLoader.h"
#include <math.h>
#include <boost/lexical_cast.hpp>

using boost::lexical_cast;
PsiBaseDataMgr::PsiBaseDataMgr()
        : m_mapExchgContract(NULL)
        , m_mapSessions(NULL)
        , m_mapCommodities(NULL)
        , m_mapContracts(NULL)
        , mp_timeLoader(NULL)
        , m_reqid(1)
{
    m_mapExchgContract = PsiExchgContract::create();
    m_mapSessions = PsiSessionMap::create();
    m_mapCommodities = PsiCommodityMap::create();
    m_mapContracts = PsiContractMap::create();
    memset(&m_strategyConfig, 0, sizeof(PSIStrategyConfigStruct));
    memset(&m_runnerConfig, 0, sizeof(PSIRunnerConfigStruct));
    memset(&m_resourceScheduleParams, 0, sizeof(PSIResourceScheduleParamsStruct));
    // 初始化上海委托明细
//    m_shOrderDetails = new TORALEV2API::CTORATstpLev2OrderDetailField*[SH_ORDER_TOTAL_LENGTH];
//    for(int i = 0; i < SH_ORDER_TOTAL_LENGTH; i++){
//        m_shOrderDetails[i] = new TORALEV2API::CTORATstpLev2OrderDetailField[INTERVAL_LENGTH];
//    }
/*
    m_shOrderDetails = new int*[SH_ORDER_TOTAL_LENGTH];
    for(int i = 0; i < SH_ORDER_TOTAL_LENGTH; i++){
        m_shOrderDetails[i] = new int[INTERVAL_LENGTH];
    }
    m_shOrderDetailIndex = 0;
    printf("[PsiBaseDataMgr] Init BaseDataMgr shOrderDetails Success!\n");
    fflush(stdout);
//    // 初始化上海委托附属明细
//    m_shOrderDetailSub = new PSILev2OrderDetailSubStruct*[SH_ORDER_TOTAL_LENGTH];
//    for(int i = 0; i < SH_ORDER_TOTAL_LENGTH; i++){
//        m_shOrderDetailSub[i] = new PSILev2OrderDetailSubStruct[INTERVAL_LENGTH];
//    }
//    printf("[PsiBaseDataMgr] Init BaseDataMgr shOrderDetailSub Success!\n");
//    fflush(stdout);

    // 初始化深圳委托明细
//    m_szOrderDetails = new TORALEV2API::CTORATstpLev2OrderDetailField*[SZ_ORDER_TOTAL_LENGTH];
//    for(int i = 0; i < SZ_ORDER_TOTAL_LENGTH; i++){
//        m_szOrderDetails[i] = new TORALEV2API::CTORATstpLev2OrderDetailField[INTERVAL_LENGTH];
//    }
    m_szOrderDetails = new int*[SZ_ORDER_TOTAL_LENGTH];
    for(int i = 0; i < SZ_ORDER_TOTAL_LENGTH; i++){
        m_szOrderDetails[i] = new int[INTERVAL_LENGTH];
    }
    m_szOrderDetailIndex = 0;
    printf("[PsiBaseDataMgr] Init BaseDataMgr szOrderDetails Success!\n");
    fflush(stdout);
//    // 初始化深圳委托附属明细
//    m_szOrderDetailSub = new PSILev2OrderDetailSubStruct*[SZ_ORDER_TOTAL_LENGTH];
//    for(int i = 0; i < SZ_ORDER_TOTAL_LENGTH; i++){
//        m_szOrderDetailSub[i] = new PSILev2OrderDetailSubStruct[INTERVAL_LENGTH];
//    }
//    printf("[PsiBaseDataMgr] Init BaseDataMgr szOrderDetailSub Success!\n");
//    fflush(stdout);

    // 初始化上海逐笔成交明细
//    m_shTransactionDetails = new TORALEV2API::CTORATstpLev2TransactionField*[SH_TRANS_TOTAL_LENGTH];
//    for(int i = 0; i < SH_TRANS_TOTAL_LENGTH; i++){
//        m_shTransactionDetails[i] = new TORALEV2API::CTORATstpLev2TransactionField[INTERVAL_LENGTH];
//    }
    m_shTransactionDetails = new int*[SH_TRANS_TOTAL_LENGTH];
    for(int i = 0; i < SH_TRANS_TOTAL_LENGTH; i++){
        m_shTransactionDetails[i] = new int[INTERVAL_LENGTH];
    }
    m_shTransactionDetailIndex = 0;
    printf("[PsiBaseDataMgr] Init BaseDataMgr shTransactionDetails Success!\n");
    fflush(stdout);
//    // 初始化上海逐笔成交附属明细
//    m_shTransactionDetailSub = new PSILev2TransactionSubStruct*[SH_TRANS_TOTAL_LENGTH];
//    for(int i = 0; i < SH_TRANS_TOTAL_LENGTH; i++){
//        m_shTransactionDetailSub[i] = new PSILev2TransactionSubStruct[INTERVAL_LENGTH];
//    }
//    printf("[PsiBaseDataMgr] Init BaseDataMgr shTransactionDetailSub Success!\n");
//    fflush(stdout);

    // 初始化深圳逐笔成交明细
//    m_szTransactionDetails = new TORALEV2API::CTORATstpLev2TransactionField*[SZ_TRANS_TOTAL_LENGTH];
//    for(int i = 0; i < SZ_TRANS_TOTAL_LENGTH; i++){
//        m_szTransactionDetails[i] = new TORALEV2API::CTORATstpLev2TransactionField[INTERVAL_LENGTH];
//    }
    m_szTransactionDetails = new int*[SZ_TRANS_TOTAL_LENGTH];
    for(int i = 0; i < SZ_TRANS_TOTAL_LENGTH; i++){
        m_szTransactionDetails[i] = new int[INTERVAL_LENGTH];
    }
    m_szTransactionDetailIndex = 0;
    printf("[PsiBaseDataMgr] Init BaseDataMgr szTransactionDetails Success!\n");
    fflush(stdout);
//    // 初始化深圳逐笔成交附属明细
//    m_szTransactionDetailSub = new PSILev2TransactionSubStruct*[SZ_TRANS_TOTAL_LENGTH];
//    for(int i = 0; i < SZ_TRANS_TOTAL_LENGTH; i++){
//        m_szTransactionDetailSub[i] = new PSILev2TransactionSubStruct[INTERVAL_LENGTH];
//    }
//    printf("[PsiBaseDataMgr] Init BaseDataMgr szTransactionDetailSub Success!\n");
//    fflush(stdout);
 */

}

PsiBaseDataMgr::~PsiBaseDataMgr()
{
    if (m_mapExchgContract)
    {
        m_mapExchgContract->release();
        m_mapExchgContract = NULL;
    }

    if (m_mapSessions)
    {
        m_mapSessions->release();
        m_mapSessions = NULL;
    }

    if (m_mapCommodities)
    {
        m_mapCommodities->release();
        m_mapCommodities = NULL;
    }

    if(m_mapContracts)
    {
        m_mapContracts->release();
        m_mapContracts = NULL;
    }
}

/**
 * 初始化
 * @param psiTimeLoader
 * @return
 */
bool PsiBaseDataMgr::init(PsiTimeLoader* psiTimeLoader){
    if(NULL == mp_timeLoader){
        mp_timeLoader = psiTimeLoader;
    }
    return true;
}

/**
 * 从文件加载基础数据
 * @param filename
 * @return
 */
bool PsiBaseDataMgr::loadSessions(const char* filename)
{
    if (!StdFile::exists(filename))
    {
        // loge("Trading sessions configuration file {} not exists", filename);
        return false;
    }

    PsiVariant* root = PsiCfgLoader::load_from_file(filename);
    if (root == NULL)
    {
        // loge("Loading session config file {} failed", filename);
        return false;
    }
    std::vector<int> periods;
    periods.push_back(KLINE_MINUTE1);
    periods.push_back(KLINE_MINUTE3);
    periods.push_back(KLINE_MINUTE5);
    // periods.push_back(KLINE_MINUTE10);
    periods.push_back(KLINE_MINUTE15);
    // periods.push_back(KLINE_MINUTE30);
    // periods.push_back(KLINE_MINUTE60);
    // periods.push_back(KLINE_DAY);
    for(const std::string& id : root->memberNames())
    {
        PsiVariant* jVal = root->get(id);

        const char* name = jVal->getCString("name");
        // logi("Loading session  {}", name);
        int32_t offset = jVal->getInt32("offset");
        int32_t type = jVal->getInt32("type");

        PsiSessionInfo* sInfo = PsiSessionInfo::create(id.c_str(), name, offset, type);

        if (jVal->has("auction"))
        {
            PsiVariant* jAuc = jVal->get("auction");
            sInfo->setAuctionTime(jAuc->getUInt32("from"), jAuc->getUInt32("to"));
        }
        else if (jVal->has("auctions"))
        {
            PsiVariant* jAucs = jVal->get("auctions");
            for (uint32_t i = 0; i < jAucs->size(); i++)
            {
                PsiVariant* jSec = jAucs->get(i);
                sInfo->addAuctionTime(jSec->getUInt32("from"), jSec->getUInt32("to"));
            }
        }

        PsiVariant* jSecs = jVal->get("sections");
        if (jSecs == NULL || !jSecs->isArray())
            continue;

        for (uint32_t i = 0; i < jSecs->size(); i++)
        {
            PsiVariant* jSec = jSecs->get(i);
            sInfo->addTradingSection(jSec->getUInt32("from"), jSec->getUInt32("to"));
        }

        // 初始化分钟线对应列表
        for(int i= 0; i < periods.size(); i++){
            sInfo->initMinuteDistribution(periods[i]);
            // logi("period: {}", periods[i]); for debug
            // for(auto t:sInfo->getPeriodMinList(periods[i])) {
            //     logi("{}", t);
            // }
            // logi("");
        }

        m_mapSessions->add(id.c_str(), sInfo);
    }
    root->release();

    return true;
}


/**
 * 从文件加载假期列表
 * @param filename
 * @return
 */
bool PsiBaseDataMgr::loadHolidays(const char* filename){

    return true;
}

/**
 * 获取品种的交易时间
 * @param sid
 * @return
 */
PsiSessionInfo*	PsiBaseDataMgr::getSession(const char* sid){
    return (PsiSessionInfo*)m_mapSessions->get(sid);
}

/**
 * 从JSON数据加载基础数据
 * @param content
 * @return
 */
bool PsiBaseDataMgr::loadJsonCommodities(const char* content){

    if (!content)
    {
        printf("Contracts configuration file not exists\n");
        return false;
    }

    PsiVariant* root = PsiCfgLoader::load_from_content(content);
    if (root == NULL)
    {
        printf("Loading contracts config file failed\n");
        return false;
    }
    int memberCount = 0;

    m_codes.reserve(max_contract_index);

    for(const std::string& exchg : root->memberNames())
    {
        PsiVariant* jExchg = root->get(exchg);

        for(const std::string& code : jExchg->memberNames())
        {
            PsiVariant* jcInfo = jExchg->get(code);

            std::string pid = jcInfo->getCString("product");
            PsiContractInfo* cInfo = PsiContractInfo::create(code.c_str(),
                                                             jcInfo->getCString("name"),
                                                             jcInfo->getCString("exchg"),
                                                             pid.c_str());
            // 获取session
            std::string session = jcInfo->getCString("session");
            if(session.empty()){
                session = "SD0930";
            }
            PsiSessionInfo* sInfo = getSession(session.c_str());
            cInfo->setSessionInfo(sInfo);
            uint32_t maxMktQty = 1000000;
            uint32_t maxLmtQty = 1000000;
            uint32_t minMktQty = 1;
            uint32_t minLmtQty = 1;
            if (jcInfo->has("maxmarketqty"))
                maxMktQty = jcInfo->getUInt32("maxmarketqty");
            if (jcInfo->has("maxlimitqty"))
                maxLmtQty = jcInfo->getUInt32("maxlimitqty");
            if (jcInfo->has("minmarketqty"))
                minMktQty = jcInfo->getUInt32("minmarketqty");
            if (jcInfo->has("minlimitqty"))
                minLmtQty = jcInfo->getUInt32("minlimitqty");
            cInfo->setVolumeLimits(maxMktQty, maxLmtQty, minMktQty, minLmtQty);

            uint32_t opendate = 0;
            uint32_t expiredate = 0;
            if (jcInfo->has("opendate"))
                opendate = jcInfo->getUInt32("opendate");
            if (jcInfo->has("expiredate"))
                expiredate = jcInfo->getUInt32("expiredate");
            cInfo->setDates(opendate, expiredate);

            double lMargin = 0;
            double sMargin = 0;
            if (jcInfo->has("longmarginratio"))
                lMargin = jcInfo->getDouble("longmarginratio");
            if (jcInfo->has("shortmarginratio"))
                sMargin = jcInfo->getDouble("shortmarginratio");
            cInfo->setMarginRatios(lMargin, sMargin);


            // 添加股票基本信息的新增
            double limitUpPrice = 0.00;
            double limitDownPrice = 0.00;
            if (jcInfo->has("limitupprice")){
                limitUpPrice = std::stod(jcInfo->getString("limitupprice"));
            }

            if (jcInfo->has("limitdownprice")){
                limitDownPrice = std::stod(jcInfo->getString("limitdownprice"));
            }
            double preClosePrice = 0.00;
            if (jcInfo->has("precloseprice"))
                preClosePrice = std::stod(jcInfo->getString("precloseprice"));
            cInfo->setLimitPrices(limitUpPrice, limitDownPrice, preClosePrice);

            std::string contractStatus = "上市";
            if (jcInfo->has("contractstatus"))
                contractStatus = jcInfo->getCString("contractstatus");
            std::string tradingStatus = "交易";
            if (jcInfo->has("tradingstatus"))
                tradingStatus = jcInfo->getCString("tradingstatus");
            int listingSector = 1;
            if (jcInfo->has("listingsector"))
                listingSector = jcInfo->getUInt32("listingsector");
            cInfo->setContractInfo(contractStatus.c_str(), tradingStatus.c_str(), listingSector);

            int64_t volume10d = 0;
            if (jcInfo->has("volume_10d"))
                volume10d = jcInfo->getInt64("volume_10d");
            int64_t maxValidVolume = 0;
            if (jcInfo->has("max_valid_volume"))
                maxValidVolume = jcInfo->getInt64("max_valid_volume");
            cInfo->setVolume(volume10d, maxValidVolume);
            // logi("[PsiBaseDataMgr] Contracts StockCode {} limitUpPrice {} limitdownprice {} volume_10d {} max_valid_volume {}", code.c_str(), limitUpPrice, limitDownPrice, volume10d, maxValidVolume);
            PsiContractList* contractList = (PsiContractList*)m_mapExchgContract->get(std::string(cInfo->getExchg()));
            if (contractList == NULL)
            {
                contractList = PsiContractList::create();
                m_mapExchgContract->add(std::string(cInfo->getExchg()), contractList, false);
            }
            contractList->add(std::string(cInfo->getCode()), cInfo, false);

            std::string key = std::string(cInfo->getCode());
            PsiArray* ayInst = (PsiArray*)m_mapContracts->get(key);
            if(ayInst == NULL)
            {
                ayInst = PsiArray::create();
                m_mapContracts->add(key, ayInst, false);
            }

            ayInst->append(cInfo, true);
            m_mapCodes.insert(std::make_pair(std::string(cInfo->getCode()), memberCount));
            m_codes[code.c_str()] = memberCount;
            memberCount++;
        }
    }

    printf("Contracts configuration file loaded, %d exchanges\n", m_mapExchgContract->size());
    root->release();
    return true;
}

/**
 * 从JSON数据加载基础数据
 * @param content
 * @return
 */
bool PsiBaseDataMgr::editJsonCommodities(const char* content){

    if (!content)
    {
        printf("Contracts configuration file not exists\n");
        return false;
    }

    PsiVariant* root = PsiCfgLoader::load_from_content(content);
    if (root == NULL)
    {
        printf("Loading contracts config file failed\n");
        return false;
    }
    for(const std::string& exchg : root->memberNames())
    {
        PsiVariant* jExchg = root->get(exchg);

        for(const std::string& code : jExchg->memberNames())
        {
            PsiVariant* jcInfo = jExchg->get(code);

            std::string pid = jcInfo->getCString("product");
            PsiContractInfo* cInfo = getContract(code.c_str());

            // 添加股票基本信息的新增
            double limitUpPrice = 0.00;
            double limitDownPrice = 0.00;
            if (jcInfo->has("limitupprice")){
                limitUpPrice = std::stod(jcInfo->getString("limitupprice"));
            }

            if (jcInfo->has("limitdownprice")){
                limitDownPrice = std::stod(jcInfo->getString("limitdownprice"));
            }
            printf("Contracts StockCode %s limitUpPrice %f limitdownprice %f \n", code.c_str(), limitUpPrice, limitDownPrice);
            double preClosePrice = 0.00;
            if (jcInfo->has("precloseprice"))
                preClosePrice = std::stod(jcInfo->getString("precloseprice"));
            cInfo->setLimitPrices(limitUpPrice, limitDownPrice, preClosePrice);

            int64_t volume10d = 0;
            if (jcInfo->has("volume_10d"))
                volume10d = jcInfo->getInt64("volume_10d");
            int64_t maxValidVolume = 0;
            if (jcInfo->has("max_valid_volume"))
                maxValidVolume = jcInfo->getInt64("max_valid_volume");
            cInfo->setVolume(volume10d, maxValidVolume);
        }
    }

    printf("Contracts configuration file edit, %d exchanges\n", m_mapExchgContract->size());
    root->release();
    return true;
}

/**
 * 从JSON数据加载品种列表
 * @param content
 * @return
 */
bool PsiBaseDataMgr::loadJsonSessions(const char* content){

    return true;
}

/**
 * 从JSON数据加载假期列表
 * @param content
 * @return
 */
bool PsiBaseDataMgr::loadJsonHolidays(const char* content){

    return true;
}

/**
 * 从JSON数据加载合约列表
 * @param content
 * @return
 */
bool PsiBaseDataMgr::loadJsonContracts(const char* content){

    return true;
}

/**
 * 通过code 获取当前股票的所有信息
 * @param code
 * @return
 */
PsiContractInfo* PsiBaseDataMgr::getContract(const char* code){
    auto lKey = std::string(code);

    auto it = m_mapContracts->find(lKey);
    if (it == m_mapContracts->end())
        return NULL;

    PsiArray* ayInst = (PsiArray*)it->second;
    if (ayInst == NULL || ayInst->size() == 0)
        return NULL;

    return (PsiContractInfo*)ayInst->at(0);
}

/**
 * 获取所有股票
 * @param exchg
 * @return
 */
PsiArray* PsiBaseDataMgr::getContracts(const char* exchg){
    PsiArray* ay = PsiArray::create();
    if(strlen(exchg) > 0)
    {
        auto it = m_mapExchgContract->find(std::string(exchg));
        if (it != m_mapExchgContract->end())
        {
            PsiContractList* contractList = (PsiContractList*)it->second;
            auto it2 = contractList->begin();
            for (; it2 != contractList->end(); it2++)
            {
                ay->append(it2->second, true);
            }
        }
    }
    else
    {
        auto it = m_mapExchgContract->begin();
        for (; it != m_mapExchgContract->end(); it++)
        {
            PsiContractList* contractList = (PsiContractList*)it->second;
            auto it2 = contractList->begin();
            for (; it2 != contractList->end(); it2++)
            {
                ay->append(it2->second, true);
            }
        }
    }

    return ay;
}

/**
 * 通过code获取当前股票的占位index
 * @param code
 * @return
 */
int PsiBaseDataMgr::getContractIndex(const char* code){
    auto it = m_mapCodes.find(code);
    if (it == m_mapCodes.end())
        return -1;

    return it->second;
}

/**
 * 通过注册的行情ID获取当前行情的所有参数
 * @param parserId
 * @return
 */
PSIParserParamsStruct& PsiBaseDataMgr::getParserParams(const char* parserId) {
    //TODO 通过注册的行情ID获取当前行情的所有参数
    auto it = m_mapParserParams.find(parserId);
    if (it == m_mapParserParams.end()){
        PSIParserParamsStruct pParserParams;
        return pParserParams;
    }
    return it->second;
}


/**
 * 设置行情模块的解析参数
 * @param parserId
 * @param params
 */
void PsiBaseDataMgr::setParserParams(const char* parserId, PSIParserParamsStruct params){
    //TODO 设置行情模块的解析参数
    m_mapParserParams.insert(std::make_pair(parserId, params));
}

/**
 * 通过注册的交易ID获取当前交易的所有参数
 * @param traderId
 * @return
 */
PSIRunnerParamsStruct& PsiBaseDataMgr::getRunnerParams(const char* runnerId){
    auto it = m_mapRunnerParams.find(runnerId);
    if (it == m_mapRunnerParams.end()){
        PSIRunnerParamsStruct pRunnerParams;
        return pRunnerParams;
    }
    return it->second;
}

/**
 * 设置交易模块的解析参数
 * @param RunnerId
 * @param params
 */
void PsiBaseDataMgr::setRunnerParams(const char* runnerId, PSIRunnerParamsStruct params){
    m_mapRunnerParams.insert(std::make_pair(runnerId, params));
}

/**
 * 通过注册的交易ID获取当前交易的所有参数
 * @param traderId
 * @return
 */
PSITraderParamsStruct& PsiBaseDataMgr::getTraderParams(const char* traderId){
    // 通过注册的交易ID获取当前交易的所有参数
    auto it = m_mapTraderParams.find(traderId);
    if (it == m_mapTraderParams.end()){
        PSITraderParamsStruct psiTraderParams;
        return psiTraderParams;
    }
    return it->second;
}

/**
 * 设置交易模块的解析参数
 * @param traderId
 * @param params
 */
void PsiBaseDataMgr::setTraderParams(const char* traderId, PSITraderParamsStruct params){
    // 设置交易模块的解析参数
    m_mapTraderParams.insert(std::make_pair(traderId, params));
}

/**
 * 获取资源调度的数据结构
 * @return
 */
PSIResourceScheduleParamsStruct& PsiBaseDataMgr::getResourceScheduleParamsStruct(){
    return m_resourceScheduleParams;
}

/**
 * 获取Runner的配置信息
 * @return
 */
PSIRunnerConfigStruct& PsiBaseDataMgr::getRunnerConfig(){
    return m_runnerConfig;
}

/**
 * 获取策略的配置信息
 * @return
 */
PSIStrategyConfigStruct& PsiBaseDataMgr::getStrategyConfig(){
    return m_strategyConfig;
}

/**
 * 通过注册的策略获取当前策略的所有参数
 * @param straCode
 * @return
 */
PSIStrategyParamsStruct& PsiBaseDataMgr::getStrategyParams(const char* straCode){
    auto it = m_mapStrategyParams.find(straCode);
    if (it == m_mapStrategyParams.end()){
        PSIStrategyParamsStruct paramsStruct;
        return paramsStruct;
    }
    return it->second;
}

/**
 * 设置策略的解析参数
 * @param straCode
 * @param params
 */
void PsiBaseDataMgr::setStrategyParams(const char* straCode, PSIStrategyParamsStruct params){
    m_mapStrategyParams.insert(std::make_pair(straCode, params));
}

/**
 * 通过注册的策略获取当前策略的所有参数
 * @param straCode
 * @return
 */
PSIStrategyParamsStruct& PsiBaseDataMgr::getSellStrategyParams(const char* straCode){
    auto it = m_mapSellStrategyParams.find(straCode);
    if (it == m_mapSellStrategyParams.end()){
        PSIStrategyParamsStruct paramsStruct;
        return paramsStruct;
    }
    return it->second;
}

/**
 * 设置策略的解析参数
 * @param straCode
 * @param params
 */
void PsiBaseDataMgr::setSellStrategyParams(const char* straCode, PSIStrategyParamsStruct params){
    m_mapSellStrategyParams.insert(std::make_pair(straCode, params));
}

/**
 * 通过注册的redisID获取当前行情的所有参数
 * @param parserId
 * @return
 */
PSIRedisParamsStruct& PsiBaseDataMgr::getRedisParams(const char* redisId){
    auto it = m_mapRedisParams.find(redisId);
    if (it == m_mapRedisParams.end()){
        PSIRedisParamsStruct paramsStruct;
        return paramsStruct;
    }
    return it->second;
}

/**
 * 设置redis模块的解析参数
 * @param parserId
 * @param params
 */
void PsiBaseDataMgr::setRedisParams(const char* redisId, PSIRedisParamsStruct params){
    m_mapRedisParams.insert(std::make_pair(redisId, params));
}

/**
 * 新增策略列表
 * @param straCode
 * @param straName
 */
void PsiBaseDataMgr::addStraMap(const char* straCode, const char* straName){
    m_straMap.insert(std::make_pair(straCode, straName));
}

/**
 * 获取策略列表
 * @return
 */
std::string PsiBaseDataMgr::getStra(const char* straCode){
    auto it = m_straMap.find(straCode);
    if (it == m_straMap.end()){
        return "";
    }
    return it->second;
}

/**
 * 新增卖出策略列表
 * @param straCode
 * @param straName
 */
void PsiBaseDataMgr::addSellStraMap(const char* straCode, const char* straName){
    m_sellStraMap.insert(std::make_pair(straCode, straName));
}

/**
 * 获取卖出策略列表
 * @return
 */
std::string PsiBaseDataMgr::getSellStra(const char* straCode){
    auto it = m_sellStraMap.find(straCode);
    if (it == m_sellStraMap.end()){
        return "";
    }
    return it->second;
}

/**
 * 设置 最大的合约index
 * @param index
 */
void PsiBaseDataMgr::setMaxContractIndex(int index){
    max_contract_index = index;
}

/**
 * 获取股票列表指针地址
 */
psi_hashmap<std::string,int>& PsiBaseDataMgr::getCodes(){
    return m_codes;
}

/**
 * 通过注册的redisID获取当前行情的所有参数
 * @param parserId
 * @return
 */
TORACREDITAPI::CTORATstpBrokerCreditSecurityField& PsiBaseDataMgr::getBrokerCreditSecurity(const char* stdCode){
    auto it = m_mapBrokerCreditSecurity.find(stdCode);
    if (it == m_mapBrokerCreditSecurity.end()){
        TORACREDITAPI::CTORATstpBrokerCreditSecurityField params;
        return params;
    }
    return it->second;
}

/**
 * 设置redis模块的解析参数
 * @param parserId
 * @param params
 */
void PsiBaseDataMgr::setBrokerCreditSecurity(const char* stdCode, TORACREDITAPI::CTORATstpBrokerCreditSecurityField params){
    m_mapBrokerCreditSecurity.insert(std::make_pair(stdCode, params));
}
