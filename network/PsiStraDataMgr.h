/*!
 * \file PsiStraDataMgr.cpp
 * \project	PsiStraDataMgr
 *
* \author liji<PERSON>
* \date 2024/02/05
 *
 * \brief 存储交易相关数据的管理类
 */
#pragma once
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "PsiStruct.h"
#include "PsiStraShareDataMgr.h"
#include "PsiTraderDataMgr.h"
#include "PsiStraTraderDataMgr.h"

class PsiStraDataMgr {
public:
    PsiStraDataMgr();
    ~PsiStraDataMgr();

    /**
     * \brief 初始化
     * \param trader 交易接口
     * \param strCode 股票代码
     * \param strExchg 交易所
     */
    void init(PsiTraderDataMgr* trader, PsiStraShareDataMgr* shareDataMgr);

public:
    uint32_t	m_straId; // 策略ID
    std::string m_strCode; // 存储股票代码

    std::string m_strExchg; // 存储交易所
    std::string m_straName; // 策略名称
    std::string m_straCode; // 策略code
    std::string m_straUuid; // 策略描述
    PsiTraderDataMgr* mp_traderDataMgr; // 交易接口
    PsiStraShareDataMgr* mp_shareDataMgr; // 共享数据
    PsiStraTraderDataMgr* mp_straTraderDataMgr; // 策略交易数据
    bool m_bRunning; // 运行状态
    psi_hashset<int> m_orderRecord; //存储下单记录
    // 参数相关
    int32_t         m_maxNum = 1; // 最大购买次数
    int32_t         m_maxVolume = 100; // 最大成交量
    int32_t         m_maxAmount = 50000; // 最大购买价格
    int32_t         m_orderNum = 0; //
    int32_t         m_accumulateOrderNum = 0; // 累计下单次数

    bool            m_isBuy = true; // 是否可以购买

    double          m_maxPriceProportion = 8.0; // 最大价格比例
    // 买入参数
    int32_t         m_placeOrderTime = 0; // 下单时机 1:排版 2:扫版 3:智能
    bool            m_isPlaceOrderTimeActive = false; // 是否下单时机

    int32_t         m_filterStockNum = 0; // 过滤前几个股票
    bool            m_isFilterStockNumActive = false; // 是否过滤前几个股票

    int32_t         m_maxDrainageNum = 0; // 最大排撤次数
    bool            m_isMaxDrainageNumActive = false; // 是否最大排撤次数

    double          m_maxPorderAmount = 10000; // 涨停价委托金额 LimitOrderAmount
    int64_t         m_maxPorderVolume = 0; // 涨停价委托量
    bool            m_isMaxPorderAmountActive = true; // 是否涨停价委托金额

    double          m_mainBuyAmount = 0.0; // 主买成交金额
    int64_t         m_mainBuyVolume = 0; // 主买成交量
    bool            m_isMainBuyAmountActive = false; // 是否主买成交金额

    double          m_boardOrderSellAmount = 0.0; // 板上卖单金额
    int64_t         m_boardOrderSellVolume = 0; // 板上卖单量
    bool            m_isBoardOrderSellAmountActive = false; // 是否板上卖单金额

    double          m_forceOrPressureRatio = 0.0; // 合力-压单占比
    int             m_forceOrPressure = 0; // 合力-压单量
    bool            m_isForceOrPressureRatioActive = false; // 是否合力/压单占比

    double          m_sealsAmount = 0.0; // 封单金额
    int64_t         m_sealsVolume = 0; // 封单量
    bool            m_isSealsAmountActive = false; // 封单金额

    bool            m_isOnlyHitBackseal = false; // 是否只打回封
    bool            m_isOnlyHitBacksealActive = false; // 是否只打回封

    bool            m_isTboardOptimization = false; // 是否T板优化
    bool            m_isTboardOptimizationActive = false; // 是否T板优化

    int32_t         m_maxStockNum = 0; // 最大股票数
    bool            m_isMaxStockNumActive = false; // 是否最大股票数.

    bool            m_isOpenBacklist = false; // 是否开启黑名单
    bool            m_isOpenBacklistActive = false; // 是否开启黑名单

    bool            m_isOpenSuccessAlarm = false; // 交易成功预警
    bool            m_isOpenSuccessAlarmActive = false; // 交易成功预警

    double          m_buyAmount = 0.0; // 买入金额
    int64_t         m_buyVolume = 0; // 买入量
    bool            m_isBuyAmountActive = false; // 是否买入金额
    int64_t         m_buyNum = 0; // 买入量限制
    bool            m_isBuyNumActive = false; // 是否买入量限制

    uint32_t        m_startTime = 0; // 开始时间
    bool            m_isStartTimeActive = false; // 是否时间

    uint32_t        m_endTime = 0; // 结束时间
    bool            m_isEndTimeActive = false; // 是否结束时间

    // 撤单参数
    bool            m_isAutoOrderChargeback = false; // 是否自动撤单
    bool            m_isAutoOrderChargebackActive = false; // 是否自动撤单

    double          m_boardCancelOrderSeconds = 0.0; // 板上撤单金额时间区间
    double          m_boardCancelOrderAmount = 0.0; // 板上撤单金额
    int64_t         m_boardCancelOrderVolume = 0; // 板上撤单量
    bool            m_isBoardCancelOrderAmountActive = false; // 封单撤单时间区间

    double          m_boardSmashOrderSeconds = 0.0; // 板上砸单时间区间
    double          m_boardSmashOrderAmount = 0.0; // 板上砸单金额
    int64_t         m_boardSmashOrderVolume = 0; // 板上砸单量
    bool            m_isBoardSmashOrderAmountActive = false; // 板上砸单时间区间

    double          m_fewerSealsRatioSeconds = 0.0; // 板上砸单次数
    double          m_fewerSealsRatio = 0.0; // 封单减少比例
    bool            m_isFewerSealsRatioActive = false; // 封单减少比例

    int32_t         m_autoOrderChargebackDeadline = 150000000; // 自动撤单截止时间
    bool            m_isAutoOrderChargebackDeadlineActive = false; // 自动撤单截止时间

    bool            m_isSmartOrder = false; // 是否智能撤单
    bool            m_isSmartOrderActive = false; // 是否智能撤单

    double          m_sealsTotalAmount = 0.0; // 封单总金额
    bool            m_isSealsTotalAmountActive = false; // 封单总金额

    bool            m_sealAnOrderActive = false; // 是否下回封单
    psi_hashset<int> m_sealAnOrder; // 在第几封下单
};

