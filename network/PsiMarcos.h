/*!
 * \file PsiMarcos.h
 * \project	PsiTrader<PERSON>agicWeapon
 *
 * \author liji<PERSON>
 * \date 2024/02/25
 * 
 * \brief PsiTraderMagicWeapon�����궨���ļ�
 */
#pragma once
#include <limits.h>
#include <string.h>

#ifndef NOMINMAX
#define NOMINMAX
#endif

#define MAX_INSTRUMENT_LENGTH	32
#define MAX_NAME_LENGTH	128
#define MAX_EXCHANGE_LENGTH		16

#define DEBUG 1

#define STATIC_CONVERT(x,T)		static_cast<T>(x)

#define ORDER_LENGTH 10000
#define TRANS_LENGTH 10000
// �����С interval
#define INTERVAL_LENGTH 8192
#define SH_ORDER_TOTAL_LENGTH 10000
#define SH_TRANS_TOTAL_LENGTH 10000
#define SZ_ORDER_TOTAL_LENGTH 10000
#define SZ_TRANS_TOTAL_LENGTH 10000
#define SIGNAL_ORDER_LENGTH 100
#define SIGNAL_TRANS_LENGTH 100
#define SIGNAL_CANCEL_LENGTH 100

#define TRANS_TOTAL_LENGTH 40000
#define ORDER_TOTAL_LENGTH 40000
#define CANCEL_TOTAL_LENGTH 40000
// �洢���ί�еĶ�����ʼ����
#define ORDER_DETAILS_LENGTH 1
// ��������С
//#define LENGTHS 30000000
//#define L2_LENGTHS 30000000
#define LENGTHS 3000000
#define L2_LENGTHS 30000
#define L1_LENGTHS 3000000
#define CODE_L1_LENGTHS 10000

// 定于Kline个数
#define KLINE_LENGTH 240
#define KLINE_MINUTE1 1
#define KLINE_MINUTE3 3
#define KLINE_MINUTE5 5
#define KLINE_MINUTE10 10
#define KLINE_MINUTE15 15
#define KLINE_MINUTE30 30
#define KLINE_MINUTE60 60
#define KLINE_DAY 240

#define PARSER_LOG_LENGTH 2000
#define CALCULATE_SELL_STRUCT_SIZE 1000
#define CALCULATE_SELL_SECOND_STRUCT_SIZE 600
// ��������
#define CANCEL_LENGTH 1
// �洢��������������
#define TRANS_VOLUME_LENGTH 50000000
#define MAX_STRA_SIZE 1000
#define MAX_REDIS_CACHE_SIZE 100
#define MAX_CODE_INDEX_SIZE 1000
// �洢��Ʊ����ĳ��ȣ�ÿ�������߳������ʹ�ã�
#define MAX_CODE_SIZE 5000
#define START_TIME 93000000
#define MAX_INTERVAL_SIZE 10

#define MAX_LOG_BUF_SIZE 2048

#define MIN_INT_LIMIT -2147483648
#define MAX_INT_LIMIT **********

#define CODE_ST "ST"
#define CODE_ST_S "*ST"

#define FILE_TYPE "file"

#define L2_TICK_TYPE "1"
#define L2_ORDER_TYPE "2"
#define L2_TRANSACTION_TYPE "3"

#define TRADER_EXCH_SSE "1"
#define TRADER_EXCH_SZSE "2"
#define TRADER_EXCH_SSE_SZSE "1,2"

#define HUAX_TRADER "TraderHuaX4"
#define HUAX_CREDIT_TRADER "TraderHuaxCredit"
#define CTP_TRADER "TraderCTP"

#define HUAX_PARSER_L2 "ParserHuaXL2"
#define HUAX_PARSER_L1 "ParserHuaXL1"
#define HUAX_PARSER_CTP "ParserCTP"

// ��Ʊ�˻�
#define STOCK_TRADER_ACCOUNT_TYPE "stock"
// �����˻�
#define CREDIT_TRADER_ACCOUNT_TYPE "credit"

#define EPS 1e-8
// ����һ��double�ıȽϴ�С�ĺ���
#define DOUBLE_EQ(x,v)	(((v - EPS) < x) && (x <( v + EPS)))
// ����һ��double���ڵ�����һ��double�ĺ���
#define DOUBLE_GE(x,v)	((x - v) > -EPS)
// ����һ��double�ıȽϴ�С�ĺ��� ���ڵ���


#ifndef DBL_MAX
#define DBL_MAX 1.7976931348623158e+308
#endif

#ifndef FLT_MAX
#define FLT_MAX 3.402823466e+38F        /* max value */
#endif

#ifdef _MSC_VER
#define INVALID_DOUBLE		DBL_MAX
#define INVALID_INT32		INT_MAX
#define INVALID_UINT32		UINT_MAX
#define INVALID_INT64		_I64_MAX
#define INVALID_UINT64		_UI64_MAX
#else
#define INVALID_DOUBLE		1.7976931348623158e+308 /* max value */
#define INVALID_INT32		**********
#define INVALID_UINT32		0xffffffffUL
#define INVALID_INT64		9223372036854775807LL
#define INVALID_UINT64		0xffffffffffffffffULL
#endif

#ifndef NULL
#ifdef __cplusplus
#define NULL 0
#else
#define NULL ((void *)0)
#endif
#endif

//#define NS_PTMW_BEGIN	namespace ptmw{
//#define NS_PTMW_END	}
//#define	USING_NS_PTMW	using namespace ptmw

#ifndef EXPORT_FLAG
#ifdef _MSC_VER
#	define EXPORT_FLAG __declspec(dllexport)
#else
#	define EXPORT_FLAG __attribute__((__visibility__("default")))
#endif
#endif

#ifndef PORTER_FLAG
#ifdef _MSC_VER
#	define PORTER_FLAG _cdecl
#else
#	define PORTER_FLAG
#endif
#endif

typedef unsigned long		MwUInt32;
typedef unsigned long long	MwUInt64;
typedef const char*			MwString;

#ifdef _MSC_VER
#define mw_stricmp _stricmp
#else
#define mw_stricmp strcasecmp
#endif

/*
 *	��дһ��strcpy
 *	���ĵ�Ҫ����ǲ���strcpy
 */
inline size_t mw_strcpy(char* des, const char* src, size_t len = 0)
{
    len = (len == 0) ? strlen(src) : len;
    memcpy(des, src, len);
    des[len] = '\0';
    return len;
}