#pragma once
#include <string>
#include <string.h>
#ifdef _WIN32
  #include <winsock2.h>
  #include <ws2tcpip.h>
  #include <io.h>
#else
  #include <unistd.h>
  #include <sys/types.h>
  #include <sys/socket.h>
  #include <netinet/in.h>
  #include <netinet/tcp.h>
  #include <arpa/inet.h>
  #include <fcntl.h>
#endif
#include <PsiTcpShmServerCommon.h>

#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN
template<class Derived>
TcpShmServer<Derived>::TcpShmServer(const std::string& ptcp_dir,const std::string& server_name,const ServerConf& server_conf)
    :ptcp_dir_(ptcp_dir),conf_(server_conf)
{
    strncpy(server_name_, server_name.c_str(), sizeof(server_name_) - 1);
    server_name_[sizeof(server_name_) - 1] = 0;
    mkdir(ptcp_dir_.c_str(), 0755);

    uint32_t conn_cnt = ServerConf::MaxShmConnsPerGrp * conf_.MaxShmGrps + ServerConf::MaxTcpConnsPerGrp * conf_.MaxTcpGrps;
    while(conn_cnt--) {
        Connection conn(server_conf);
        conn.init(ptcp_dir.c_str(),server_name_);
        conn_pool_.emplace_back(std::move(conn));
    }
    new_conns_.resize(conf_.MaxNewConnections);
    tcp_grps_.resize(conf_.MaxTcpGrps);
    shm_grps_.resize(conf_.MaxShmGrps);

    int cnt = 0;
    for(auto& grp : shm_grps_) {
        for(auto& conn : grp.conns) {
            conn = &conn_pool_[cnt++];
        }
    }
    for(auto& grp : tcp_grps_) {
        for(auto& conn : grp.conns) {
            conn = &conn_pool_[cnt++];
        }
    }
}
template<class Derived>
TcpShmServer<Derived>::~TcpShmServer()
{
    Stop();
}
template<class Derived>
bool TcpShmServer<Derived>::Start(const char* listen_ipv4, uint16_t listen_port)
{
    if(listenfd_ >= 0) {
        static_cast<Derived*>(this)->OnSystemError("already started", 0);
        return false;
    }

    if((listenfd_ = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("socket", errno);
        return false;
    }

    fcntl(listenfd_, F_SETFL, O_NONBLOCK);
    int yes = 1;
    if(setsockopt(listenfd_, SOL_SOCKET, SO_REUSEADDR, &yes, sizeof(yes)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("setsockopt SO_REUSEADDR", errno);
        return false;
    }
    if(conf_.TcpNoDelay && setsockopt(listenfd_, IPPROTO_TCP, TCP_NODELAY, &yes, sizeof(yes)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("setsockopt TCP_NODELAY", errno);
        return false;
    }

    struct sockaddr_in local_addr;
    local_addr.sin_family = AF_INET;
    inet_pton(AF_INET, listen_ipv4, &(local_addr.sin_addr));
    local_addr.sin_port = htons(listen_port);
    bzero(&(local_addr.sin_zero), 8);
    if(bind(listenfd_, (struct sockaddr*)&local_addr, sizeof(local_addr)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("bind", errno);
        return false;
    }
    if(listen(listenfd_, 5) < 0) {
        static_cast<Derived*>(this)->OnSystemError("listen", errno);
        return false;
    }
    return true;
}

template<class Derived>
void TcpShmServer<Derived>::PollCtl(int64_t now)
{
    // 每次轮询只接受一个新连接，如果还有可用的连接槽
    if(avail_idx_ != conf_.MaxNewConnections) {
        NewConn& conn = new_conns_[avail_idx_];
        socklen_t addr_len = sizeof(conn.addr);
        conn.fd = accept(listenfd_, (struct sockaddr*)&(conn.addr), &addr_len);
        // 忽略accept的错误，EAGAIN是预期的错误，表示没有连接准备好
        if(conn.fd >= 0) {
            fcntl(conn.fd, F_SETFL, O_NONBLOCK); // 将接受的连接设置为非阻塞模式
            conn.time = now; // 记录连接接受的时间
            avail_idx_ =  conf_.MaxNewConnections; // 标记此索引为已使用，当前不再接受新连接
        }
    }

    // 遍历所有新连接，尝试读取登录消息
    for(int i = 0; i <  conf_.MaxNewConnections; i++) {
        NewConn& conn = new_conns_[i];
        if(conn.fd < 0) {
            avail_idx_ = i; // 如果连接fd无效，将此槽位标记为可用
            continue;
        }
        int ret = ::recv(conn.fd, conn.recvbuf, sizeof(conn.recvbuf), 0);
        if(ret < 0 && errno == EAGAIN && now - conn.time <=  conf_.NewConnectionTimeout) {
            // 如果收到EAGAIN且连接在超时时间内，继续读取数据
            continue;
        }
        if(ret == sizeof(conn.recvbuf)) {
            // 如果接收到的数据大小等于预期的消息头和登录消息的大小
            conn.recvbuf[0].template ConvertByteOrder<ServerConf::ToLittleEndian>();
            if(conn.recvbuf[0].size == sizeof(MsgHeader) + sizeof(LoginMsg) &&
                conn.recvbuf[0].msg_type == LoginMsg::msg_type) {
                // 如果消息大小和类型都匹配，认为是有效的登录消息
                auto* login = (LoginMsg*)(conn.recvbuf + 1);
                login->ConvertByteOrder(); // 转换字节序
                if(login->use_shm) {
                    // 如果登录消息要求使用共享内存，处理登录消息
                    HandleLogin(now, conn, &shm_grps_[0]);
                }
                else {
                    // 否则，处理TCP组的登录消息
                    HandleLogin(now, conn, &tcp_grps_[0]);
                }
            }
        }

        // 关闭无效的连接
        if(conn.fd >= 0) {
            ::close(conn.fd);
            conn.fd = -1;
        }
        avail_idx_ = i; // 将此槽位标记为可用
    }

    // 处理共享内存组中的现有连接
    for(auto& grp : shm_grps_) {
        for(int i = 0; i < grp.live_cnt;) {
            Connection& conn = *grp.conns[i];
            conn.TcpFront(now); // 轮询心跳，忽略返回值
            if(conn.TryCloseFd()) {
                // 如果需要关闭连接，获取关闭原因并通知派生类
                int sys_errno;
                const char* reason = conn.GetCloseReason(&sys_errno);
                static_cast<Derived*>(this)->OnClientDisconnected(conn, reason, sys_errno);
                std::swap(grp.conns[i], grp.conns[--grp.live_cnt]); // 移除关闭的连接
            }
            else {
                i++;
            }
        }
    }

    // 处理TCP组中的现有连接
    for(auto& grp : tcp_grps_) {
        for(int i = 0; i < grp.live_cnt;) {
            Connection& conn = *grp.conns[i];
            if(conn.TryCloseFd()) {
                // 如果需要关闭连接，获取关闭原因并通知派生类
                int sys_errno;
                const char* reason = conn.GetCloseReason(&sys_errno);
                static_cast<Derived*>(this)->OnClientDisconnected(conn, reason, sys_errno);
                std::swap(grp.conns[i], grp.conns[--grp.live_cnt]); // 移除关闭的连接
            }
            else {
                i++;
            }
        }
    }
}
// 接受新连接：每次轮询只接受一个新的连接，并将其设置为非阻塞模式。
// 处理新连接：读取新连接的数据，如果接收到有效的登录消息，则将连接分配给共享内存组或TCP组。
// 管理现有连接：遍历现有连接，处理心跳检查和关闭无效连接。
// 连接清理：关闭无效连接，并通知派生类关于断开的连接。

template<class Derived>
void TcpShmServer<Derived>::PollTcp(int64_t now, int grpid)
{
    auto& grp = tcp_grps_[grpid];
    // force read grp.live_cnt from memory, it could have been changed by Ctl thread
    // printf("tcp group %d live_cnt %d\n", grpid, grp.live_cnt);
    asm volatile("" : "=m"(grp.live_cnt) : :);
    for(int i = 0; i < grp.live_cnt; i++) {
        // it's possible that grp.conns is being swapped by Ctl thread
        // so some live conn could be missed, some closed one could be visited
        // even some conn could be visited twice, but those're all fine
        Connection& conn = *grp.conns[i];
        MsgHeader* head = conn.TcpFront(now);
        if(head) static_cast<Derived*>(this)->OnClientMsg(conn, head);
    }
}
// 读取现有连接的数据：对于指定的TCP组，遍历所有活动连接，调用TcpFront方法读取消息头。
// 消息处理：如果读取到有效的消息头，则将连接和消息头传递给派生类处理。

template<class Derived>
void TcpShmServer<Derived>::PollShm(int grpid)
{
    auto& grp = shm_grps_[grpid];
    asm volatile("" : "=m"(grp.live_cnt) : :);
    for(int i = 0; i < grp.live_cnt; i++) {
        Connection& conn = *grp.conns[i];
        MsgHeader* head = conn.ShmFront();
        if(head) static_cast<Derived*>(this)->OnClientMsg(conn, head);
    }
}

template<class Derived>
void TcpShmServer<Derived>::CustomTcpPoll(std::function<void(Connection&)> func) {
    for(int grpid=0;grpid<conf_.MaxTcpGrps;grpid++) {
        auto& grp = tcp_grps_[grpid];
        asm volatile("" : "=m"(grp.live_cnt) : :);
        for(int i = 0; i < grp.live_cnt; i++) {
            Connection& conn = *grp.conns[i];
            func(conn);
        }
    }
}

template<class Derived>
void TcpShmServer<Derived>::Stop()
{
    if(listenfd_ < 0) {
        return;
    }
    ::close(listenfd_);
    listenfd_ = -1;
    for(int i = 0; i <  conf_.MaxNewConnections; i++) {
        int& fd = new_conns_[i].fd;
        if(fd >= 0) {
            ::close(fd);
            fd = -1;
        }
    }
    avail_idx_ = 0;
    for(auto& grp : shm_grps_) {
        for(auto& conn : grp.conns) {
            conn->Release();
        }
        grp.live_cnt = 0;
    }
    for(auto& grp : tcp_grps_) {
        for(auto& conn : grp.conns) {
            conn->Release();
        }
        grp.live_cnt = 0;
    }
}
template<class Derived>
template<uint32_t N>
void TcpShmServer<Derived>::HandleLogin(int64_t now, NewConn& conn, ConnectionGroup<N>* grps){
    MsgHeader sendbuf[1 + (sizeof(LoginRspMsg) + 7) / 8];
    sendbuf[0].size = sizeof(MsgHeader) + sizeof(LoginRspMsg);
    sendbuf[0].msg_type = LoginRspMsg::msg_type;
    sendbuf[0].ConvertByteOrder<CommonConf::ToLittleEndian>();
    auto* login_rsp = (LoginRspMsg*)(sendbuf + 1);
    strncpy(login_rsp->server_name, server_name_, sizeof(login_rsp->server_name));
    login_rsp->status = 2;
    login_rsp->error_msg[0] = 0;

    auto* login = (LoginMsg*)(conn.recvbuf + 1);
    if(login->client_name[0] == 0) {
        strncpy(login_rsp->error_msg, "Invalid client name", sizeof(login_rsp->error_msg));
        ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
        return;
    }
    login->client_name[sizeof(login->client_name) - 1] = 0;
    int grpid = static_cast<Derived*>(this)->OnNewConnection(conn.addr, login, login_rsp);
    if(grpid < 0) {
        if(login_rsp->error_msg[0] == 0) { // user didn't set error_msg? set a default one
            strncpy(login_rsp->error_msg, "Login Reject", sizeof(login_rsp->error_msg));
        }
        ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
        return;
    }
    auto& grp = grps[grpid];
    for(int i = 0; i < N; i++) {
        Connection& curconn = *grp.conns[i];
        char* remote_name = curconn.GetRemoteName();
        if(remote_name[0] == 0) { // found an unused one, use it then
            strncpy(remote_name, login->client_name, sizeof(login->client_name));
        }
        if(strncmp(remote_name, login->client_name, sizeof(login->client_name)) != 0) {
            // client name does not match
            continue;
        }
        // match
        if(i < grp.live_cnt) {
            strncpy(login_rsp->error_msg, "Already loggned on", sizeof(login_rsp->error_msg));
            ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
            return;
        }

        const char* error_msg;
        if(!curconn.OpenFile(login->use_shm, &error_msg)) {
            // we can not mmap to ptcp or chm files with filenames related to local and remote name
            static_cast<Derived*>(this)->OnClientFileError(curconn, error_msg, errno);
            strncpy(login_rsp->error_msg, "System error", sizeof(login_rsp->error_msg));
            ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
            return;
        }
        uint32_t local_ack_seq = 0;
        uint32_t local_seq_start = 0;
        uint32_t local_seq_end = 0;
        uint32_t remote_ack_seq = conn.recvbuf[0].ack_seq;
        uint32_t remote_seq_start = login->client_seq_start;
        uint32_t remote_seq_end = login->client_seq_end;
        // if server_name has changed, reset the ack_seq
        if(strncmp(login->last_server_name, server_name_, sizeof(server_name_)) != 0) {
            curconn.Reset();
            remote_ack_seq = remote_seq_start = remote_seq_end = 0;
        }
        else {
            if(!curconn.GetSeq(&local_ack_seq, &local_seq_start, &local_seq_end, &error_msg)) {
                static_cast<Derived*>(this)->OnClientFileError(curconn, error_msg, errno);
                strncpy(login_rsp->error_msg, "System error", sizeof(login_rsp->error_msg));
                ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
                return;
            }
        }
        sendbuf[0].ack_seq =Endian<CommonConf::ToLittleEndian>::Convert(local_ack_seq);
        login_rsp->server_seq_start = local_seq_start;
        login_rsp->server_seq_end = local_seq_end;
        login_rsp->ConvertByteOrder();
        if(
            !CheckAckInQueue(remote_ack_seq, local_seq_start, local_seq_end) ||
            !CheckAckInQueue(local_ack_seq, remote_seq_start, remote_seq_end)) {
            static_cast<Derived*>(this)->OnSeqNumberMismatch(curconn,
                                                                local_ack_seq,
                                                                local_seq_start,
                                                                local_seq_end,
                                                                remote_ack_seq,
                                                                remote_seq_start,
                                                                remote_seq_end);
            login_rsp->status = 1;
            ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
            return;
        }

        // send Login OK
        login_rsp->status = 0;
        if(::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL) != sizeof(sendbuf)) {
            return;
        }
        curconn.Open(conn.fd, remote_ack_seq, now);
        conn.fd = -1; // so it won't be closed by caller
        // switch to live
        std::swap(grp.conns[i], grp.conns[grp.live_cnt++]);
        static_cast<Derived*>(this)->OnClientLogon(conn.addr, curconn);
        return;
    }
    // no space for new remote name
    strncpy(login_rsp->error_msg, "Max client cnt exceeded", sizeof(login_rsp->error_msg));
    ::send(conn.fd, sendbuf, sizeof(sendbuf), MSG_NOSIGNAL);
}
template<class Derived>
bool TcpShmServer<Derived>::CheckAckInQueue(uint32_t ack_seq, uint32_t seq_start, uint32_t seq_end){
    return (int)(ack_seq - seq_start) >= 0 && (int)(seq_end - ack_seq) >= 0;
}
NS_PSI_TCP_SHM_END
