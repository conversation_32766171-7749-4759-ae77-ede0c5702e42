#!/bin/bash

# 快速启动Docker开发环境脚本
# 一键启动Ubuntu18+Qt5开发环境

# 项目配置
IMAGE_NAME="tcp-qt5-ubuntu18"
CONTAINER_NAME="tcp-qt5-dev"
PROJECT_DIR="$(pwd)"

echo "🚀 启动TCP Qt5 Ubuntu18开发环境..."

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查并构建镜像
if ! docker images | grep -q "$IMAGE_NAME"; then
    echo "📦 构建Docker镜像..."
    docker build -t $IMAGE_NAME .
fi

# 停止已存在的容器
if docker ps | grep -q "$CONTAINER_NAME"; then
    echo "🛑 停止已运行的容器..."
    docker stop $CONTAINER_NAME
fi

# 删除已存在的容器
if docker ps -a | grep -q "$CONTAINER_NAME"; then
    echo "🗑️  删除旧容器..."
    docker rm $CONTAINER_NAME
fi

# 检测X11支持
X11_ARGS=""
if [ -n "$DISPLAY" ] && [ -S "/tmp/.X11-unix" ]; then
    echo "🖥️  启用X11 GUI支持"
    X11_ARGS="-e DISPLAY=$DISPLAY -v /tmp/.X11-unix:/tmp/.X11-unix:rw"
fi

echo "🔧 启动开发容器..."

# 启动容器
docker run -it --rm \
    --name $CONTAINER_NAME \
    --hostname tcp-qt5-dev \
    -v "$PROJECT_DIR:/workspace" \
    -p 8888:8888 \
    -p 8889:8889 \
    -p 8890:8890 \
    $X11_ARGS \
    --privileged \
    $IMAGE_NAME

echo "👋 容器已退出"
