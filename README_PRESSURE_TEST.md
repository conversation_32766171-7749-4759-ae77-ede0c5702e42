# TCP服务压力测试方案

本文档描述了一个完整的TCP服务压力测试解决方案，包含20个测试场景，涵盖不同的客户端数量、消息大小和发送间隔组合。

## 📋 测试场景概览

根据压力测试方案图，本方案包含以下20个测试场景：

| 场景 | 客户端数量 | 消息大小 | 发送间隔 | 描述 |
|------|------------|----------|----------|------|
| 1-4  | 1个        | 128字节  | 10μs, 无间隔, 5分钟, 3分钟 | 单客户端基准测试 |
| 5-8  | 5个        | 128字节  | 10μs, 无间隔, 5分钟, 3分钟 | 小规模并发测试 |
| 9-12 | 10个       | 128字节  | 10μs, 无间隔, 5分钟, 3分钟 | 中等规模并发测试 |
| 13-16| 50个       | 128字节  | 10μs, 无间隔, 5分钟, 3分钟 | 大规模并发测试 |
| 17-20| 5个        | 1024字节 | 10μs, 无间隔, 5分钟, 3分钟 | 大消息负载测试 |

## 🏗️ 方案架构

### 核心组件

1. **测试脚本** (`run_pressure_test.sh`)
   - 自动化测试执行
   - 场景管理和进程控制
   - 日志收集和整理

2. **配置模板** (`config_template.yaml`)
   - 参数化配置管理
   - 动态配置生成

3. **客户端程序** (修改后的 `main.cpp`)
   - 支持命令行参数
   - 配置文件驱动
   - 无界面模式运行

4. **结果分析器** (`analyze_results.py`)
   - 自动化结果分析
   - 性能指标统计
   - 图表和报告生成

### 文件结构

```
tcp_qt5/
├── src/
│   ├── main.cpp              # 修改后的主程序（支持命令行参数）
│   ├── mainwindow.h          # 修改后的主窗口头文件
│   └── mainwindow.cpp        # 修改后的主窗口实现
├── run_pressure_test.sh      # 主测试脚本
├── config_template.yaml      # 配置文件模板
├── analyze_results.py        # 结果分析脚本
├── README_PRESSURE_TEST.md   # 本文档
└── test_results/             # 测试结果目录（自动创建）
    ├── scenario_01_1_client_128B_10us/
    ├── scenario_02_1_client_128B_no_interval/
    └── ...
```

## 🚀 快速开始

### 1. 环境准备

#### 编译客户端程序
```bash
# 确保Qt环境已配置
cd /Users/<USER>/Desktop/cui/tcp_qt5
qmake
make
```

#### 安装Python依赖（可选，用于结果分析）
```bash
# 基础分析功能
pip3 install pyyaml

# 图表生成功能（可选）
pip3 install matplotlib pandas
```

### 2. 启动TCP服务器

确保你的TCP服务器正在运行并监听指定端口（默认8080）。

### 3. 运行压力测试

#### 运行所有场景
```bash
# 给脚本执行权限
chmod +x run_pressure_test.sh

# 运行所有20个测试场景
./run_pressure_test.sh
```

#### 运行特定场景
```bash
# 只运行场景1-5
./run_pressure_test.sh --scenarios "1 2 3 4 5"

# 运行单个场景
./run_pressure_test.sh --scenarios "10"
```

#### 自定义参数
```bash
# 自定义测试持续时间和服务器地址
./run_pressure_test.sh --duration 120 --server ************* --port 9090
```

### 4. 分析测试结果

```bash
# 分析所有测试结果
python3 analyze_results.py test_results

# 指定输出目录
python3 analyze_results.py test_results -o my_analysis

# 不生成图表（如果没有安装matplotlib）
python3 analyze_results.py test_results --no-charts
```

## 📊 测试结果

### 结果目录结构

```
test_results/
├── scenario_01_1_client_128B_10us/
│   ├── client_1.log          # 客户端日志
│   ├── config_client_1.yaml  # 客户端配置
│   └── report.txt            # 场景报告
├── scenario_02_1_client_128B_no_interval/
│   └── ...
├── test_summary.txt          # 总体测试摘要
└── final_report.txt          # 最终测试报告
```

### 分析结果

运行分析脚本后，会在指定目录生成：

```
analysis_output/
├── analysis_report.txt       # 详细分析报告
├── performance_charts.png    # 性能图表（如果安装了matplotlib）
└── performance_data.csv      # 性能数据CSV文件
```

## ⚙️ 配置说明

### 测试脚本配置

在 `run_pressure_test.sh` 中可以修改以下参数：

```bash
# 测试基本参数
TEST_DURATION=60              # 每个场景测试时长（秒）
SERVER_ADDR="127.0.0.1"       # 服务器地址
SERVER_PORT=8080              # 服务器端口
MESSAGE_SIZE_SMALL=128        # 小消息大小（字节）
MESSAGE_SIZE_LARGE=1024       # 大消息大小（字节）

# 发送间隔配置（微秒）
INTERVAL_FAST=10              # 快速发送间隔
INTERVAL_NONE=0               # 无间隔
INTERVAL_5MIN=300000000       # 5分钟间隔
INTERVAL_3MIN=180000000       # 3分钟间隔
```

### 客户端配置模板

`config_template.yaml` 包含以下可配置项：

```yaml
client_name: "CLIENT_NAME"           # 客户端名称
server_addr: "SERVER_ADDR"           # 服务器地址
server_port: SERVER_PORT             # 服务器端口
message_size: MESSAGE_SIZE           # 消息大小
send_interval_us: SEND_INTERVAL_US   # 发送间隔（微秒）
max_connections: 1                   # 最大连接数
reconnect_interval: 5000             # 重连间隔（毫秒）
log_level: "INFO"                    # 日志级别
enable_statistics: true              # 启用统计
statistics_interval: 10000           # 统计间隔（毫秒）
```

## 🔧 自定义和扩展

### 添加新的测试场景

在 `run_pressure_test.sh` 中的 `scenarios` 数组添加新场景：

```bash
scenarios[
  "21:custom_test:8:2048:5000"  # 场景21：8个客户端，2048字节消息，5000μs间隔
]
```

### 修改客户端行为

1. 在 `mainwindow.h` 中添加新的配置方法
2. 在 `mainwindow.cpp` 中实现配置逻辑
3. 在 `main.cpp` 中添加对应的命令行参数

### 自定义分析指标

修改 `analyze_results.py` 中的 `analyze_client_logs` 方法，添加新的性能指标计算。

## 📈 性能指标说明

### 核心指标

- **吞吐量** (msg/s): 每秒成功处理的消息数量
- **成功率** (%): 成功消息占总消息的百分比
- **响应时间** (ms): 消息发送到接收响应的时间
- **连接成功率** (%): 成功建立连接的百分比

### 分析维度

1. **负载扩展性**: 客户端数量增加时的性能变化
2. **消息大小影响**: 不同消息大小对性能的影响
3. **发送频率影响**: 不同发送间隔对系统的影响
4. **稳定性分析**: 长时间运行的稳定性表现

## 🐛 故障排除

### 常见问题

1. **编译错误**
   ```bash
   # 检查Qt环境
   qmake --version
   
   # 清理重新编译
   make clean
   qmake
   make
   ```

2. **客户端启动失败**
   ```bash
   # 检查可执行文件
   ls -la tcp_qt5
   
   # 手动测试单个客户端
   ./tcp_qt5 --config config_template.yaml --client-name test --headless
   ```

3. **服务器连接失败**
   ```bash
   # 检查服务器状态
   telnet 127.0.0.1 8080
   
   # 检查防火墙设置
   netstat -an | grep 8080
   ```

4. **Python分析脚本错误**
   ```bash
   # 检查Python版本
   python3 --version
   
   # 安装缺失依赖
   pip3 install -r requirements.txt
   ```

### 日志调试

- 客户端日志：`test_results/scenario_XX/client_Y.log`
- 测试脚本日志：`test_results/test_execution.log`
- 错误日志：`test_results/error.log`

## 📝 最佳实践

### 测试前准备

1. **系统资源检查**
   ```bash
   # 检查可用内存
   free -h
   
   # 检查CPU使用率
   top
   
   # 检查网络连接数限制
   ulimit -n
   ```

2. **服务器预热**
   - 先运行小规模测试确保服务器正常
   - 监控服务器资源使用情况

3. **环境隔离**
   - 在专用测试环境中运行
   - 避免其他应用程序干扰

### 测试执行

1. **逐步增加负载**
   - 从小规模场景开始
   - 观察系统行为变化
   - 识别性能拐点

2. **多次运行**
   - 每个场景运行多次取平均值
   - 识别性能波动情况

3. **实时监控**
   - 监控服务器CPU、内存、网络使用率
   - 观察客户端日志输出
   - 及时发现异常情况

### 结果分析

1. **对比分析**
   - 横向对比不同场景的性能
   - 纵向分析性能随负载的变化趋势

2. **瓶颈识别**
   - 找出性能下降的临界点
   - 分析限制因素（CPU、内存、网络、应用逻辑）

3. **优化建议**
   - 基于测试结果提出系统优化建议
   - 制定容量规划方案

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. 所有依赖是否正确安装
2. 配置文件格式是否正确
3. 服务器是否正常运行
4. 系统资源是否充足
5. 网络连接是否正常

---

**注意**: 本测试方案会产生大量网络连接和数据传输，请确保在合适的测试环境中运行，避免对生产系统造成影响。