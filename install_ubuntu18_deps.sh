#!/bin/bash

# Ubuntu 18.04 依赖安装脚本
# 用于安装Qt5、CMake、Boost等必要依赖

set -e  # 遇到错误立即退出

echo "=== Ubuntu 18.04 TCP Qt项目依赖安装脚本 ==="
echo "正在更新软件包列表..."

# 更新软件包列表
sudo apt update

echo "正在安装基础开发工具..."
# 安装基础开发工具
sudo apt install -y \
    build-essential \
    cmake \
    git \
    pkg-config \
    wget \
    curl

echo "正在安装Qt5开发环境..."
# 安装Qt5开发环境
sudo apt install -y \
    qt5-default \
    qtbase5-dev \
    qtbase5-dev-tools \
    libqt5widgets5 \
    libqt5network5 \
    libqt5core5a \
    libqt5gui5

echo "正在安装Boost库..."
# 安装Boost库
sudo apt install -y \
    libboost-all-dev \
    libboost-filesystem-dev \
    libboost-system-dev

echo "正在安装其他必要依赖..."
# 安装其他依赖
sudo apt install -y \
    libjsoncpp-dev \
    rapidjson-dev \
    libx11-xcb1 \
    libxkbcommon-x11-0 \
    libxcb1 \
    libxcb-render0 \
    libxcb-shape0 \
    libxcb-xfixes0

echo "正在检查CMake版本..."
# 检查CMake版本，Ubuntu 18.04默认的CMake可能版本较低
CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
echo "当前CMake版本: $CMAKE_VERSION"

# 如果CMake版本低于3.16，提供升级建议
if [ "$(printf '%s\n' "3.16" "$CMAKE_VERSION" | sort -V | head -n1)" = "3.16" ]; then
    echo "✓ CMake版本满足要求 (>= 3.16)"
else
    echo "⚠️  CMake版本过低，建议升级到3.16+："
    echo "   可以从 https://cmake.org/download/ 下载最新版本"
    echo "   或者修改CMakeLists.txt中的最低版本要求"
fi

echo ""
echo "=== 安装完成 ==="
echo "现在可以尝试编译项目："
echo "  mkdir -p build && cd build"
echo "  cmake .."
echo "  make -j\$(nproc)"
echo ""
echo "如果遇到Qt相关问题，请确保设置了正确的环境变量："
echo "  export QT_SELECT=qt5"
echo ""
echo "运行程序时如果遇到显示问题，请确保："
echo "  1. 使用 ssh -X 或 ssh -Y 连接服务器"
echo "  2. 设置了 DISPLAY 环境变量"
echo "  3. 安装了必要的X11库"
