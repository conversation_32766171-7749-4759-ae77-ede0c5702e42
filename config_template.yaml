# TCP客户端配置模板
# 此文件将被测试脚本复制并修改参数

tcpshm:
  trader_client:
    choice: "trader_client"
    client_name: "test_client"  # 将被脚本替换为具体客户端名称
    server_addr: "127.0.0.1"   # 将被脚本替换为实际服务器地址
    server_port: 8888          # 将被脚本替换为实际服务器端口
    use_shm: false
    tcp_recv_buf_init_size: 65536
    tcp_recv_buf_max_size: 1048576
    tcp_no_delay: true
    connection_timeout: 10
    heart_beat_interval: 30
    
    # 测试相关配置
    test_mode: true            # 启用测试模式
    message_size: 128          # TCP消息大小(字节)
    auto_send: true            # 自动发送消息
    send_interval_us: 10       # 发送间隔(微秒)，将被脚本参数覆盖
    max_messages: 0            # 最大消息数，0表示无限制
    log_level: "INFO"          # 日志级别: DEBUG, INFO, WARN, ERROR