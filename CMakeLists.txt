cmake_minimum_required(VERSION 3.10)  # 降低要求以兼容Ubuntu 18.04
project(tcp_qt5)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 自动处理 Qt 的 moc
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 查找 Qt (支持Qt5和Qt6)
find_package(Qt6 COMPONENTS Core Gui Widgets Network)
if(NOT Qt6_FOUND)
    find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets Network)
    set(QT_VERSION_MAJOR 5)
else()
    set(QT_VERSION_MAJOR 6)
endif()

# 线程库
find_package(Threads REQUIRED)

# 查找 Boost（文件系统与系统组件）
find_package(Boost REQUIRED COMPONENTS filesystem system)

# 可选：RapidJSON（header-only）
find_package(RapidJSON QUIET)

# 添加网络框架源文件
add_subdirectory(network)

# 主程序可执行文件
add_executable(${PROJECT_NAME}
    src/main.cpp
    src/mainwindow.h
    src/mainwindow.cpp
    src/mainwindow.ui
    src/tcpshmclientwrapper.h
    src/tcpshmclientwrapper.cpp
    src/globals.h
    src/res.qrc
)

# 包含路径（避免硬编码 Windows 绝对路径）
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/network
    ${Boost_INCLUDE_DIRS}
)
if(RapidJSON_FOUND)
    target_include_directories(${PROJECT_NAME} PRIVATE ${RapidJSON_INCLUDE_DIRS})
endif()

# 链接库
if(QT_VERSION_MAJOR EQUAL 6)
    target_link_libraries(${PROJECT_NAME} PRIVATE
        Qt6::Core
        Qt6::Gui
        Qt6::Widgets
        Qt6::Network
        PsiNetwork
        ${Boost_LIBRARIES}
        Threads::Threads
    )
else()
    target_link_libraries(${PROJECT_NAME} PRIVATE
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
        Qt5::Network
        PsiNetwork
        ${Boost_LIBRARIES}
        Threads::Threads
    )
endif()

# Windows 特定设置
if(WIN32)
    target_link_libraries(${PROJECT_NAME} PRIVATE
        ws2_32
        advapi32
    )
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
endif()
