#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCP压力测试结果分析脚本
功能:
1. 解析测试日志文件
2. 统计性能指标
3. 生成测试报告
4. 创建性能图表

作者: AI Assistant
"""

import os
import sys
import re
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 尝试导入可选的图表库
try:
    import matplotlib.pyplot as plt
    import pandas as pd
    HAS_PLOTTING = True
except ImportError:
    HAS_PLOTTING = False
    print("警告: matplotlib 或 pandas 未安装，将跳过图表生成")
    print("安装命令: pip install matplotlib pandas")

class TestResultAnalyzer:
    """测试结果分析器"""
    
    def __init__(self, results_dir: str):
        """
        初始化分析器
        :param results_dir: 测试结果目录
        """
        self.results_dir = Path(results_dir)
        self.scenarios = []
        self.performance_data = {}
        
    def scan_scenarios(self) -> List[Dict]:
        """
        扫描所有测试场景目录
        :return: 场景信息列表
        """
        scenarios = []
        
        if not self.results_dir.exists():
            print(f"错误: 结果目录不存在: {self.results_dir}")
            return scenarios
            
        # 查找所有场景目录
        for scenario_dir in self.results_dir.glob("scenario_*"):
            if scenario_dir.is_dir():
                scenario_info = self._parse_scenario_info(scenario_dir)
                if scenario_info:
                    scenarios.append(scenario_info)
                    
        # 按场景编号排序
        scenarios.sort(key=lambda x: x.get('scenario_num', 0))
        self.scenarios = scenarios
        return scenarios
    
    def _parse_scenario_info(self, scenario_dir: Path) -> Optional[Dict]:
        """
        解析单个场景信息
        :param scenario_dir: 场景目录路径
        :return: 场景信息字典
        """
        try:
            # 从目录名解析基本信息
            dir_name = scenario_dir.name
            match = re.match(r'scenario_(\d+)_(.+)', dir_name)
            if not match:
                return None
                
            scenario_num = int(match.group(1))
            scenario_desc = match.group(2).replace('_', ' ')
            
            # 读取报告文件获取详细信息
            report_file = scenario_dir / "report.txt"
            scenario_info = {
                'scenario_num': scenario_num,
                'scenario_desc': scenario_desc,
                'scenario_dir': scenario_dir,
                'client_count': 0,
                'send_interval': 0,
                'duration': 0,
                'client_logs': []
            }
            
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 解析客户端数量
                match = re.search(r'客户端数量: (\d+)', content)
                if match:
                    scenario_info['client_count'] = int(match.group(1))
                    
                # 解析发送间隔
                match = re.search(r'发送间隔: (\d+)μs', content)
                if match:
                    scenario_info['send_interval'] = int(match.group(1))
                    
                # 解析测试持续时间
                match = re.search(r'测试持续时间: (\d+)s', content)
                if match:
                    scenario_info['duration'] = int(match.group(1))
            
            # 查找客户端日志文件
            for log_file in scenario_dir.glob("client_*.log"):
                scenario_info['client_logs'].append(log_file)
                
            return scenario_info
            
        except Exception as e:
            print(f"解析场景信息失败 {scenario_dir}: {e}")
            return None
    
    def analyze_client_logs(self, scenario_info: Dict) -> Dict:
        """
        分析客户端日志文件
        :param scenario_info: 场景信息
        :return: 分析结果
        """
        analysis = {
            'total_messages': 0,
            'successful_messages': 0,
            'failed_messages': 0,
            'connection_attempts': 0,
            'successful_connections': 0,
            'avg_response_time': 0,
            'max_response_time': 0,
            'min_response_time': float('inf'),
            'errors': [],
            'throughput_msgs_per_sec': 0
        }
        
        response_times = []
        
        for log_file in scenario_info['client_logs']:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 统计消息数量
                messages = re.findall(r'发送消息|消息发送|send.*message', content, re.IGNORECASE)
                analysis['total_messages'] += len(messages)
                
                # 统计成功消息
                success_msgs = re.findall(r'发送成功|消息成功|send.*success', content, re.IGNORECASE)
                analysis['successful_messages'] += len(success_msgs)
                
                # 统计连接尝试
                connections = re.findall(r'连接|connect', content, re.IGNORECASE)
                analysis['connection_attempts'] += len(connections)
                
                # 统计成功连接
                success_conns = re.findall(r'连接成功|connected.*success', content, re.IGNORECASE)
                analysis['successful_connections'] += len(success_conns)
                
                # 提取响应时间(如果日志中有的话)
                response_time_matches = re.findall(r'响应时间[：:]?\s*(\d+(?:\.\d+)?)\s*ms', content)
                for match in response_time_matches:
                    rt = float(match)
                    response_times.append(rt)
                    analysis['max_response_time'] = max(analysis['max_response_time'], rt)
                    analysis['min_response_time'] = min(analysis['min_response_time'], rt)
                
                # 提取错误信息
                error_matches = re.findall(r'错误|error|失败|failed', content, re.IGNORECASE)
                analysis['errors'].extend(error_matches)
                
            except Exception as e:
                print(f"分析日志文件失败 {log_file}: {e}")
        
        # 计算失败消息数
        analysis['failed_messages'] = analysis['total_messages'] - analysis['successful_messages']
        
        # 计算平均响应时间
        if response_times:
            analysis['avg_response_time'] = sum(response_times) / len(response_times)
        else:
            analysis['min_response_time'] = 0
            
        # 计算吞吐量
        if scenario_info['duration'] > 0:
            analysis['throughput_msgs_per_sec'] = analysis['successful_messages'] / scenario_info['duration']
            
        return analysis
    
    def generate_summary_report(self) -> str:
        """
        生成汇总报告
        :return: 报告内容
        """
        if not self.scenarios:
            return "没有找到测试场景数据"
            
        report_lines = [
            "======================================",
            "TCP压力测试结果分析报告",
            "======================================",
            f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"测试场景总数: {len(self.scenarios)}",
            f"结果目录: {self.results_dir}",
            "",
            "=== 场景汇总 ==="
        ]
        
        # 分析每个场景
        total_messages = 0
        total_successful = 0
        total_failed = 0
        
        for scenario in self.scenarios:
            analysis = self.analyze_client_logs(scenario)
            self.performance_data[scenario['scenario_num']] = analysis
            
            total_messages += analysis['total_messages']
            total_successful += analysis['successful_messages']
            total_failed += analysis['failed_messages']
            
            success_rate = (analysis['successful_messages'] / analysis['total_messages'] * 100) if analysis['total_messages'] > 0 else 0
            
            report_lines.extend([
                f"\n场景 {scenario['scenario_num']}: {scenario['scenario_desc']}",
                f"  客户端数量: {scenario['client_count']}",
                f"  发送间隔: {scenario['send_interval']}μs",
                f"  测试时长: {scenario['duration']}s",
                f"  总消息数: {analysis['total_messages']}",
                f"  成功消息: {analysis['successful_messages']}",
                f"  失败消息: {analysis['failed_messages']}",
                f"  成功率: {success_rate:.2f}%",
                f"  吞吐量: {analysis['throughput_msgs_per_sec']:.2f} msg/s",
                f"  平均响应时间: {analysis['avg_response_time']:.2f}ms"
            ])
            
            if analysis['errors']:
                report_lines.append(f"  错误数量: {len(analysis['errors'])}")
        
        # 总体统计
        overall_success_rate = (total_successful / total_messages * 100) if total_messages > 0 else 0
        
        report_lines.extend([
            "",
            "=== 总体统计 ===",
            f"总消息数: {total_messages}",
            f"总成功数: {total_successful}",
            f"总失败数: {total_failed}",
            f"总体成功率: {overall_success_rate:.2f}%",
            "",
            "=== 性能分析建议 ===",
            "1. 关注成功率低于95%的场景，可能存在性能瓶颈",
            "2. 比较不同客户端数量下的吞吐量变化趋势",
            "3. 分析响应时间随负载增加的变化情况",
            "4. 检查错误日志，识别常见的失败原因",
            "5. 监控服务器资源使用情况，确定系统瓶颈"
        ])
        
        return "\n".join(report_lines)
    
    def generate_performance_charts(self, output_dir: Path):
        """
        生成性能图表
        :param output_dir: 输出目录
        """
        if not HAS_PLOTTING:
            print("跳过图表生成: 缺少必要的库")
            return
            
        if not self.performance_data:
            print("没有性能数据，跳过图表生成")
            return
            
        try:
            # 准备数据
            scenarios = []
            client_counts = []
            throughputs = []
            success_rates = []
            avg_response_times = []
            
            for scenario_num, data in self.performance_data.items():
                scenario_info = next((s for s in self.scenarios if s['scenario_num'] == scenario_num), None)
                if scenario_info:
                    scenarios.append(f"场景{scenario_num}")
                    client_counts.append(scenario_info['client_count'])
                    throughputs.append(data['throughput_msgs_per_sec'])
                    success_rate = (data['successful_messages'] / data['total_messages'] * 100) if data['total_messages'] > 0 else 0
                    success_rates.append(success_rate)
                    avg_response_times.append(data['avg_response_time'])
            
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('TCP压力测试性能分析', fontsize=16)
            
            # 吞吐量图表
            ax1.bar(scenarios, throughputs, color='skyblue')
            ax1.set_title('各场景吞吐量对比')
            ax1.set_ylabel('吞吐量 (msg/s)')
            ax1.tick_params(axis='x', rotation=45)
            
            # 成功率图表
            ax2.bar(scenarios, success_rates, color='lightgreen')
            ax2.set_title('各场景成功率对比')
            ax2.set_ylabel('成功率 (%)')
            ax2.set_ylim(0, 100)
            ax2.tick_params(axis='x', rotation=45)
            
            # 响应时间图表
            ax3.plot(scenarios, avg_response_times, marker='o', color='orange')
            ax3.set_title('各场景平均响应时间')
            ax3.set_ylabel('响应时间 (ms)')
            ax3.tick_params(axis='x', rotation=45)
            
            # 客户端数量 vs 吞吐量散点图
            ax4.scatter(client_counts, throughputs, color='red', alpha=0.7)
            ax4.set_title('客户端数量 vs 吞吐量')
            ax4.set_xlabel('客户端数量')
            ax4.set_ylabel('吞吐量 (msg/s)')
            
            plt.tight_layout()
            
            # 保存图表
            chart_file = output_dir / "performance_charts.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            print(f"性能图表已保存: {chart_file}")
            
            plt.close()
            
        except Exception as e:
            print(f"生成图表失败: {e}")
    
    def export_csv_data(self, output_dir: Path):
        """
        导出CSV格式的性能数据
        :param output_dir: 输出目录
        """
        try:
            csv_file = output_dir / "performance_data.csv"
            
            with open(csv_file, 'w', encoding='utf-8') as f:
                # 写入表头
                f.write("场景编号,场景描述,客户端数量,发送间隔(μs),测试时长(s),总消息数,成功消息,失败消息,成功率(%),吞吐量(msg/s),平均响应时间(ms)\n")
                
                # 写入数据
                for scenario in self.scenarios:
                    data = self.performance_data.get(scenario['scenario_num'], {})
                    success_rate = (data.get('successful_messages', 0) / data.get('total_messages', 1) * 100) if data.get('total_messages', 0) > 0 else 0
                    
                    f.write(f"{scenario['scenario_num']},"
                           f"{scenario['scenario_desc']},"
                           f"{scenario['client_count']},"
                           f"{scenario['send_interval']},"
                           f"{scenario['duration']},"
                           f"{data.get('total_messages', 0)},"
                           f"{data.get('successful_messages', 0)},"
                           f"{data.get('failed_messages', 0)},"
                           f"{success_rate:.2f},"
                           f"{data.get('throughput_msgs_per_sec', 0):.2f},"
                           f"{data.get('avg_response_time', 0):.2f}\n")
            
            print(f"CSV数据已导出: {csv_file}")
            
        except Exception as e:
            print(f"导出CSV失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TCP压力测试结果分析工具')
    parser.add_argument('results_dir', help='测试结果目录路径')
    parser.add_argument('-o', '--output', default='analysis_output', help='分析结果输出目录')
    parser.add_argument('--no-charts', action='store_true', help='不生成图表')
    parser.add_argument('--no-csv', action='store_true', help='不导出CSV')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = TestResultAnalyzer(args.results_dir)
    
    # 扫描测试场景
    scenarios = analyzer.scan_scenarios()
    if not scenarios:
        print("没有找到测试场景数据")
        return 1
        
    print(f"找到 {len(scenarios)} 个测试场景")
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    # 生成汇总报告
    print("生成汇总报告...")
    summary_report = analyzer.generate_summary_report()
    
    # 保存报告
    report_file = output_dir / "analysis_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(summary_report)
    
    print(f"分析报告已保存: {report_file}")
    
    # 生成图表
    if not args.no_charts:
        print("生成性能图表...")
        analyzer.generate_performance_charts(output_dir)
    
    # 导出CSV
    if not args.no_csv:
        print("导出CSV数据...")
        analyzer.export_csv_data(output_dir)
    
    print("\n分析完成！")
    print(f"结果保存在: {output_dir}")
    
    # 显示简要统计
    print("\n=== 简要统计 ===")
    total_scenarios = len(scenarios)
    total_clients = sum(s['client_count'] for s in scenarios)
    print(f"测试场景数: {total_scenarios}")
    print(f"总客户端实例: {total_clients}")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())