#include "mainwindow.h"
// #include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    // ui->setupUi(this);
    this->resize(1200,800);
    this->setWindowTitle(Global::APP_TITLE);
    this->setWindowIcon(QPixmap(":/Images/logo.png"));
    client = new TcpShmClientWrapper(this);
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);  // 调用父类的 resizeEvent
    client->setFixedSize(this->width(),this->height());
}

MainWindow::~MainWindow()
{
    // delete ui;
    delete client;
}
