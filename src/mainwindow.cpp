#include "mainwindow.h"
#include <QApplication>
#include <QWidget>
#include <QResizeEvent>
#include <QPixmap>
// #include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), m_serverPort(8888), m_sendInterval(1000)
{
    // ui->setupUi(this);
    this->resize(1200,800);
    this->setWindowTitle(Global::APP_TITLE);
    this->setWindowIcon(QPixmap(":/Images/logo.png"));
    client = new TcpShmClientWrapper(this);
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);  // 调用父类的 resizeEvent
    client->setFixedSize(this->width(),this->height());
}

MainWindow::~MainWindow()
{
    // delete ui;
    delete client;
}

/**
 * 设置配置文件路径
 * @param configFile 配置文件路径
 */
void MainWindow::setConfigFile(const QString& configFile)
{
    m_configFile = configFile;
    if (client) {
        // 如果需要，可以通知客户端重新加载配置
        // client->loadConfig(configFile);
    }
}

/**
 * 设置客户端名称
 * @param clientName 客户端名称
 */
void MainWindow::setClientName(const QString& clientName)
{
    m_clientName = clientName;
    // 更新窗口标题以包含客户端名称
    QString title = QString("%1 - %2").arg(Global::APP_TITLE).arg(clientName);
    this->setWindowTitle(title);
}

/**
 * 设置服务器地址和端口
 * @param address 服务器地址
 * @param port 服务器端口
 */
void MainWindow::setServerAddress(const QString& address, int port)
{
    m_serverAddress = address;
    m_serverPort = port;
    if (client) {
        // 如果需要，可以通知客户端更新服务器地址
        // client->setServerAddress(address, port);
    }
}

/**
 * 设置发送间隔
 * @param intervalUs 发送间隔(微秒)
 */
void MainWindow::setSendInterval(int intervalUs)
{
    m_sendInterval = intervalUs;
    if (client) {
        // 如果需要，可以通知客户端更新发送间隔
        // client->setSendInterval(intervalUs);
    }
}
