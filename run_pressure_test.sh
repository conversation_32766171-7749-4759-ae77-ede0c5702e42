#!/bin/bash
# TCP服务压力测试脚本
# 作者: AI Assistant
# 功能: 自动化执行TCP服务器压力测试，支持多客户端、不同间隔的测试场景

set -e  # 遇到错误立即退出

# =============================================================================
# 配置参数
# =============================================================================
TEST_DURATION=180  # 测试持续时间(秒) - 3分钟
SERVER_ADDR="127.0.0.1"
SERVER_PORT=8888
RESULT_DIR="test_results"
APP_NAME="MyNetworkApp"
BUILD_DIR="build"
CONFIG_TEMPLATE="config_template.yaml"
TCP_MESSAGE_SIZE=128  # TCP正文字节数

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 测试场景定义 (客户端数量:发送间隔微秒:场景描述)
# =============================================================================
declare -a TEST_SCENARIOS=(
    "1:10:1客户端_间隔10us"
    "1:10:1客户端_间隔10us_重复"
    "5:10:5客户端_间隔10us"
    "5:10:5客户端_间隔10us_重复"
    "10:10:10客户端_间隔10us"
    "10:10:10客户端_间隔10us_重复"
    "1:0:1客户端_无间隔"
    "1:0:1客户端_无间隔_重复"
    "5:0:5客户端_无间隔"
    "5:0:5客户端_无间隔_重复"
    "10:0:10客户端_无间隔"
    "10:0:10客户端_无间隔_重复"
    "1:10:1客户端_间隔10us_第3轮"
    "5:10:5客户端_间隔10us_第3轮"
    "10:10:10客户端_间隔10us_第3轮"
    "1:0:1客户端_无间隔_第3轮"
    "5:0:5客户端_无间隔_第3轮"
    "10:0:10客户端_无间隔_第3轮"
    "1:10:1客户端_间隔10us_第4轮"
    "5:0:5客户端_无间隔_第4轮"
)

# =============================================================================
# 工具函数
# =============================================================================

# 函数: log_info
# 作用: 输出信息日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 函数: log_success
# 作用: 输出成功日志
log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 函数: log_warning
# 作用: 输出警告日志
log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 函数: log_error
# 作用: 输出错误日志
log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 函数: cleanup_processes
# 作用: 清理所有测试进程
cleanup_processes() {
    log_info "正在清理测试进程..."
    
    # 杀死所有MyNetworkApp进程
    pkill -f "$APP_NAME" 2>/dev/null || true
    
    # 等待进程完全退出
    sleep 2
    
    # 强制杀死残留进程
    pkill -9 -f "$APP_NAME" 2>/dev/null || true
    
    log_success "进程清理完成"
}

# 函数: check_prerequisites
# 作用: 检查测试前置条件
check_prerequisites() {
    log_info "检查测试前置条件..."
    
    # 检查应用程序是否存在
    if [ ! -f "$BUILD_DIR/$APP_NAME" ]; then
        log_error "应用程序 $BUILD_DIR/$APP_NAME 不存在，请先编译项目"
        exit 1
    fi
    
    # 检查配置模板是否存在
    if [ ! -f "$CONFIG_TEMPLATE" ]; then
        log_error "配置模板 $CONFIG_TEMPLATE 不存在"
        exit 1
    fi
    
    # 创建结果目录
    mkdir -p "$RESULT_DIR"
    
    log_success "前置条件检查通过"
}

# 函数: generate_config
# 作用: 生成客户端配置文件
# 参数: $1=客户端ID, $2=发送间隔(微秒)
generate_config() {
    local client_id=$1
    local interval_us=$2
    local config_file="config_client_${client_id}.yaml"
    
    # 复制模板并修改参数
    cp "$CONFIG_TEMPLATE" "$config_file"
    
    # 使用sed替换配置参数
    sed -i '' "s/\"client_name\": \"test_client\"/\"client_name\": \"test_client_${client_id}\"/g" "$config_file"
    sed -i '' "s/\"server_addr\": \"127.0.0.1\"/\"server_addr\": \"${SERVER_ADDR}\"/g" "$config_file"
    sed -i '' "s/\"server_port\": 8888/\"server_port\": ${SERVER_PORT}/g" "$config_file"
    
    echo "$config_file"
}

# 函数: run_test_scenario
# 作用: 执行单个测试场景
# 参数: $1=客户端数量, $2=发送间隔, $3=场景名称, $4=场景编号
run_test_scenario() {
    local client_count=$1
    local interval_us=$2
    local scenario_name="$3"
    local scenario_num=$4
    
    log_info "开始执行测试场景 [$scenario_num/20]: $scenario_name"
    log_info "参数: 客户端数量=$client_count, 发送间隔=${interval_us}μs, 持续时间=${TEST_DURATION}s"
    
    # 创建场景结果目录
    local scenario_dir="$RESULT_DIR/scenario_${scenario_num}_$(echo $scenario_name | tr ' ' '_')"
    mkdir -p "$scenario_dir"
    
    # 存储客户端进程ID
    local client_pids=()
    local config_files=()
    
    # 启动多个客户端实例
    for i in $(seq 1 $client_count); do
        local config_file=$(generate_config $i $interval_us)
        config_files+=("$config_file")
        
        log_info "启动客户端 $i/$client_count (配置: $config_file)"
        
        # 启动客户端并重定向输出
        "./$BUILD_DIR/$APP_NAME" \
            --config="$config_file" \
            --client-name="test_client_$i" \
            --test-duration=$TEST_DURATION \
            --send-interval=$interval_us \
            > "$scenario_dir/client_${i}.log" 2>&1 &
        
        local pid=$!
        client_pids+=($pid)
        log_info "客户端 $i 已启动 (PID: $pid)"
        
        # 短暂延迟避免同时启动造成资源竞争
        sleep 0.1
    done
    
    # 记录测试开始时间
    local start_time=$(date +%s)
    echo "$start_time" > "$scenario_dir/start_time.txt"
    
    log_info "所有客户端已启动，等待测试完成 (${TEST_DURATION}秒)..."
    
    # 监控测试进度
    local elapsed=0
    while [ $elapsed -lt $TEST_DURATION ]; do
        sleep 10
        elapsed=$((elapsed + 10))
        local remaining=$((TEST_DURATION - elapsed))
        log_info "测试进行中... 已用时: ${elapsed}s, 剩余: ${remaining}s"
    done
    
    # 记录测试结束时间
    local end_time=$(date +%s)
    echo "$end_time" > "$scenario_dir/end_time.txt"
    
    # 等待额外5秒确保所有数据处理完成
    log_info "等待数据处理完成..."
    sleep 5
    
    # 优雅关闭客户端进程
    log_info "正在关闭客户端进程..."
    for pid in "${client_pids[@]}"; do
        if kill -0 $pid 2>/dev/null; then
            kill -TERM $pid 2>/dev/null || true
        fi
    done
    
    # 等待进程退出
    sleep 3
    
    # 强制杀死未退出的进程
    for pid in "${client_pids[@]}"; do
        if kill -0 $pid 2>/dev/null; then
            kill -9 $pid 2>/dev/null || true
        fi
    done
    
    # 清理配置文件
    for config_file in "${config_files[@]}"; do
        rm -f "$config_file"
    done
    
    # 生成测试报告
    generate_scenario_report "$scenario_dir" "$scenario_name" $client_count $interval_us
    
    log_success "测试场景 [$scenario_num/20] 完成: $scenario_name"
    echo "----------------------------------------"
}

# 函数: generate_scenario_report
# 作用: 生成单个场景的测试报告
generate_scenario_report() {
    local scenario_dir="$1"
    local scenario_name="$2"
    local client_count=$3
    local interval_us=$4
    
    local report_file="$scenario_dir/report.txt"
    
    {
        echo "=== TCP压力测试报告 ==="
        echo "场景名称: $scenario_name"
        echo "测试时间: $(date)"
        echo "客户端数量: $client_count"
        echo "发送间隔: ${interval_us}μs"
        echo "测试持续时间: ${TEST_DURATION}s"
        echo "TCP消息大小: ${TCP_MESSAGE_SIZE}字节"
        echo ""
        
        if [ -f "$scenario_dir/start_time.txt" ] && [ -f "$scenario_dir/end_time.txt" ]; then
            local start_time=$(cat "$scenario_dir/start_time.txt")
            local end_time=$(cat "$scenario_dir/end_time.txt")
            local actual_duration=$((end_time - start_time))
            echo "实际测试时长: ${actual_duration}s"
        fi
        
        echo ""
        echo "=== 客户端日志文件 ==="
        for log_file in "$scenario_dir"/client_*.log; do
            if [ -f "$log_file" ]; then
                echo "- $(basename "$log_file")"
            fi
        done
        
        echo ""
        echo "=== 测试结果分析 ==="
        echo "注意: 详细的性能数据请查看各客户端日志文件"
        
    } > "$report_file"
    
    log_info "场景报告已生成: $report_file"
}

# 函数: generate_final_report
# 作用: 生成最终测试报告
generate_final_report() {
    local final_report="$RESULT_DIR/final_report.txt"
    
    log_info "生成最终测试报告..."
    
    {
        echo "======================================"
        echo "TCP服务器压力测试 - 最终报告"
        echo "======================================"
        echo "测试完成时间: $(date)"
        echo "总测试场景数: ${#TEST_SCENARIOS[@]}"
        echo "单场景持续时间: ${TEST_DURATION}s"
        echo "总测试时长: 约 $((${#TEST_SCENARIOS[@]} * (TEST_DURATION + 30) / 60)) 分钟"
        echo ""
        
        echo "=== 测试场景列表 ==="
        local i=1
        for scenario in "${TEST_SCENARIOS[@]}"; do
            IFS=':' read -r clients interval desc <<< "$scenario"
            echo "$i. $desc (客户端:$clients, 间隔:${interval}μs)"
            ((i++))
        done
        
        echo ""
        echo "=== 结果文件位置 ==="
        echo "测试结果目录: $RESULT_DIR/"
        echo "各场景详细报告: $RESULT_DIR/scenario_*/report.txt"
        echo "客户端日志: $RESULT_DIR/scenario_*/client_*.log"
        
        echo ""
        echo "=== 后续分析建议 ==="
        echo "1. 查看各场景的report.txt了解基本信息"
        echo "2. 分析client_*.log文件获取详细性能数据"
        echo "3. 对比不同客户端数量和发送间隔的性能表现"
        echo "4. 关注连接稳定性和消息传输成功率"
        echo "5. 监控服务器资源使用情况(CPU、内存、网络)"
        
    } > "$final_report"
    
    log_success "最终报告已生成: $final_report"
}

# =============================================================================
# 主执行流程
# =============================================================================

# 函数: main
# 作用: 主执行函数
main() {
    echo "======================================"
    echo "TCP服务器压力测试脚本"
    echo "======================================"
    
    # 设置退出时清理
    trap cleanup_processes EXIT
    
    # 检查前置条件
    check_prerequisites
    
    # 清理可能存在的进程
    cleanup_processes
    
    log_info "开始执行 ${#TEST_SCENARIOS[@]} 个测试场景"
    log_info "预计总耗时: 约 $((${#TEST_SCENARIOS[@]} * (TEST_DURATION + 30) / 60)) 分钟"
    
    # 执行所有测试场景
    local scenario_num=1
    for scenario in "${TEST_SCENARIOS[@]}"; do
        # 解析场景参数
        IFS=':' read -r client_count interval_us scenario_name <<< "$scenario"
        
        # 执行测试场景
        run_test_scenario $client_count $interval_us "$scenario_name" $scenario_num
        
        # 场景间休息
        if [ $scenario_num -lt ${#TEST_SCENARIOS[@]} ]; then
            log_info "场景间休息 10 秒..."
            sleep 10
        fi
        
        ((scenario_num++))
    done
    
    # 生成最终报告
    generate_final_report
    
    log_success "所有测试场景执行完成！"
    log_info "测试结果保存在: $RESULT_DIR/"
    log_info "查看最终报告: $RESULT_DIR/final_report.txt"
}

# 检查是否直接执行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi