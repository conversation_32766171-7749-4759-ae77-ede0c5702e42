# Ubuntu 18.04 + Qt5 开发环境 Dockerfile
# 用于模拟线上环境进行项目开发和测试

FROM ubuntu:18.04

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV QT_SELECT=qt5

# 设置工作目录
WORKDIR /workspace

# 配置国内镜像源以提高下载速度
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# 更新软件包列表并安装基础工具
RUN apt-get update && apt-get install -y \
    # 基础开发工具
    build-essential \
    cmake \
    git \
    pkg-config \
    wget \
    curl \
    vim \
    nano \
    htop \
    tree \
    # 时区设置
    tzdata \
    # 网络工具
    net-tools \
    iputils-ping \
    telnet \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装Qt5开发环境
RUN apt-get update && apt-get install -y \
    qt5-default \
    qtbase5-dev \
    qtbase5-dev-tools \
    libqt5widgets5 \
    libqt5network5 \
    libqt5core5a \
    libqt5gui5 \
    # X11相关库（用于GUI显示）
    libx11-xcb1 \
    libxkbcommon-x11-0 \
    libxcb1 \
    libxcb-render0 \
    libxcb-shape0 \
    libxcb-xfixes0 \
    libxcb-icccm4 \
    libxcb-image0 \
    libxcb-keysyms1 \
    libxcb-randr0 \
    libxcb-render-util0 \
    libxcb-xinerama0 \
    libxcb-xkb1 \
    libxkbcommon0 \
    libxkbcommon-x11-0 \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Boost库
RUN apt-get update && apt-get install -y \
    libboost-all-dev \
    libboost-filesystem-dev \
    libboost-system-dev \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装其他必要依赖
RUN apt-get update && apt-get install -y \
    libjsoncpp-dev \
    rapidjson-dev \
    # Redis客户端库（可选）
    libhiredis-dev \
    # 线程库
    libpthread-stubs0-dev \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 检查并显示安装的版本信息
RUN echo "=== 环境信息 ===" && \
    echo "Ubuntu版本: $(cat /etc/os-release | grep VERSION_ID)" && \
    echo "CMake版本: $(cmake --version | head -n1)" && \
    echo "Qt版本: $(qmake --version | grep Qt)" && \
    echo "GCC版本: $(gcc --version | head -n1)" && \
    echo "Boost版本: $(dpkg -l | grep libboost-dev | awk '{print $3}')" && \
    echo "=================="

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "=== TCP Qt5 Ubuntu18 开发环境 ==="\n\
echo "工作目录: /workspace"\n\
echo "Qt版本: $(qmake --version | grep Qt)"\n\
echo "CMake版本: $(cmake --version | head -n1)"\n\
echo ""\n\
echo "常用命令:"\n\
echo "  构建项目: mkdir -p build && cd build && cmake .. && make -j$(nproc)"\n\
echo "  运行程序: ./build/tcp_qt5"\n\
echo "  查看文件: tree -L 2"\n\
echo ""\n\
echo "注意: 如需运行GUI程序，请确保主机支持X11转发"\n\
echo "================================"\n\
exec "$@"' > /entrypoint.sh && chmod +x /entrypoint.sh

# 设置入口点
ENTRYPOINT ["/entrypoint.sh"]

# 默认启动bash
CMD ["/bin/bash"]

# 暴露常用端口（可根据需要调整）
EXPOSE 8888 8889 8890

# 设置用户（可选，避免权限问题）
# RUN useradd -m -s /bin/bash developer && \
#     chown -R developer:developer /workspace
# USER developer
