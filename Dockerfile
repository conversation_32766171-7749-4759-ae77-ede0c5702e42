# Ubuntu 18.04 + Qt5 开发环境 Dockerfile (简化版)
# 用于模拟线上环境进行项目开发和测试

FROM --platform=linux/amd64 ubuntu:18.04

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV QT_SELECT=qt5

# 设置工作目录
WORKDIR /workspace

# 使用官方源，分步安装以提高成功率
RUN apt-get update && apt-get install -y \
    # 基础开发工具
    build-essential \
    cmake \
    git \
    wget \
    curl \
    vim \
    # 时区设置
    tzdata \
    # 网络工具
    net-tools \
    iputils-ping \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装Qt5开发环境（分步安装以提高成功率）
RUN apt-get update && apt-get install -y \
    qt5-default \
    qtbase5-dev \
    qtbase5-dev-tools \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Qt5运行时库
RUN apt-get update && apt-get install -y \
    libqt5widgets5 \
    libqt5network5 \
    libqt5core5a \
    libqt5gui5 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装基础依赖库
RUN apt-get update && apt-get install -y \
    libboost-filesystem-dev \
    libboost-system-dev \
    libjsoncpp-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 检查并显示安装的版本信息
RUN echo "=== 环境信息 ===" && \
    echo "Ubuntu版本: $(cat /etc/os-release | grep VERSION_ID)" && \
    echo "CMake版本: $(cmake --version | head -n1)" && \
    echo "Qt版本: $(qmake --version | grep Qt)" && \
    echo "GCC版本: $(gcc --version | head -n1)" && \
    echo "Boost版本: $(dpkg -l | grep libboost-dev | awk '{print $3}')" && \
    echo "=================="

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "=== TCP Qt5 Ubuntu18 开发环境 ==="\n\
echo "工作目录: /workspace"\n\
echo "Qt版本: $(qmake --version | grep Qt)"\n\
echo "CMake版本: $(cmake --version | head -n1)"\n\
echo ""\n\
echo "常用命令:"\n\
echo "  构建项目: mkdir -p build && cd build && cmake .. && make -j$(nproc)"\n\
echo "  运行程序: ./build/tcp_qt5"\n\
echo "  查看文件: tree -L 2"\n\
echo ""\n\
echo "注意: 如需运行GUI程序，请确保主机支持X11转发"\n\
echo "================================"\n\
exec "$@"' > /entrypoint.sh && chmod +x /entrypoint.sh

# 设置入口点
ENTRYPOINT ["/entrypoint.sh"]

# 默认启动bash
CMD ["/bin/bash"]

# 暴露常用端口（可根据需要调整）
EXPOSE 8888 8889 8890

# 设置用户（可选，避免权限问题）
# RUN useradd -m -s /bin/bash developer && \
#     chown -R developer:developer /workspace
# USER developer
