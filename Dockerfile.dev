# Ubuntu 18.04 开发环境 Dockerfile
# 用于TCP Qt5项目开发

FROM ubuntu:18.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /workspace

# 更新软件包列表并安装基础开发工具
RUN apt-get update && apt-get install -y \
    # 基础开发工具
    build-essential \
    cmake \
    git \
    vim \
    # 网络和调试工具
    curl \
    wget \
    net-tools \
    iputils-ping \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 验证安装
RUN gcc --version && \
    cmake --version && \
    git --version

# 设置默认命令
CMD ["/bin/bash"]

# 添加标签信息
LABEL maintainer="TCP Qt5 Project"
LABEL description="Ubuntu 18.04 development environment with basic tools"
LABEL version="1.0"
