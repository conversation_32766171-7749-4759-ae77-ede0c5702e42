#!/bin/bash

# TCP压力测试快速启动脚本
# 这个脚本会自动检查环境、编译程序并运行测试

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查Qt环境
    if ! command_exists qmake; then
        log_error "qmake 未找到，请确保Qt开发环境已安装"
        exit 1
    fi
    
    # 检查make
    if ! command_exists make; then
        log_error "make 未找到，请安装构建工具"
        exit 1
    fi
    
    # 检查Python3
    if ! command_exists python3; then
        log_warning "python3 未找到，将跳过结果分析"
        SKIP_ANALYSIS=true
    else
        SKIP_ANALYSIS=false
    fi
    
    log_success "环境检查完成"
}

# 编译程序
build_program() {
    log_info "编译客户端程序..."
    
    # 清理之前的构建
    if [ -f "Makefile" ]; then
        make clean >/dev/null 2>&1 || true
    fi
    
    # 生成Makefile
    if ! qmake >/dev/null 2>&1; then
        log_error "qmake 执行失败"
        exit 1
    fi
    
    # 编译
    if ! make >/dev/null 2>&1; then
        log_error "编译失败，请检查代码和依赖"
        exit 1
    fi
    
    # 检查可执行文件
    if [ ! -f "tcp_qt5" ]; then
        log_error "可执行文件 tcp_qt5 未生成"
        exit 1
    fi
    
    log_success "程序编译完成"
}

# 检查服务器连接
check_server() {
    local server_addr="${1:-127.0.0.1}"
    local server_port="${2:-8080}"
    
    log_info "检查服务器连接 ${server_addr}:${server_port}..."
    
    if command_exists nc; then
        if ! nc -z "$server_addr" "$server_port" 2>/dev/null; then
            log_warning "无法连接到服务器 ${server_addr}:${server_port}"
            log_warning "请确保TCP服务器正在运行"
            read -p "是否继续测试？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "测试已取消"
                exit 0
            fi
        else
            log_success "服务器连接正常"
        fi
    else
        log_warning "nc 命令未找到，跳过服务器连接检查"
    fi
}

# 安装Python依赖
install_python_deps() {
    if [ "$SKIP_ANALYSIS" = true ]; then
        return
    fi
    
    log_info "检查Python依赖..."
    
    if [ -f "requirements.txt" ]; then
        if ! python3 -c "import yaml" >/dev/null 2>&1; then
            log_info "安装Python依赖..."
            if ! pip3 install -r requirements.txt >/dev/null 2>&1; then
                log_warning "Python依赖安装失败，将跳过结果分析"
                SKIP_ANALYSIS=true
            else
                log_success "Python依赖安装完成"
            fi
        else
            log_success "Python依赖已满足"
        fi
    fi
}

# 运行测试
run_test() {
    local scenarios="$1"
    local duration="$2"
    local server_addr="$3"
    local server_port="$4"
    
    log_info "开始运行压力测试..."
    
    # 构建测试命令
    local test_cmd="./run_pressure_test.sh"
    
    if [ -n "$scenarios" ]; then
        test_cmd="$test_cmd --scenarios \"$scenarios\""
    fi
    
    if [ -n "$duration" ]; then
        test_cmd="$test_cmd --duration $duration"
    fi
    
    if [ -n "$server_addr" ]; then
        test_cmd="$test_cmd --server $server_addr"
    fi
    
    if [ -n "$server_port" ]; then
        test_cmd="$test_cmd --port $server_port"
    fi
    
    # 给测试脚本执行权限
    chmod +x run_pressure_test.sh
    
    # 执行测试
    log_info "执行命令: $test_cmd"
    if eval "$test_cmd"; then
        log_success "压力测试完成"
    else
        log_error "压力测试失败"
        exit 1
    fi
}

# 分析结果
analyze_results() {
    if [ "$SKIP_ANALYSIS" = true ]; then
        log_warning "跳过结果分析"
        return
    fi
    
    if [ ! -d "test_results" ]; then
        log_warning "测试结果目录不存在，跳过分析"
        return
    fi
    
    log_info "分析测试结果..."
    
    if python3 analyze_results.py test_results; then
        log_success "结果分析完成"
        log_info "分析报告保存在: analysis_output/"
    else
        log_warning "结果分析失败"
    fi
}

# 显示帮助信息
show_help() {
    echo "TCP压力测试快速启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -s, --scenarios SCENARIOS    指定要运行的场景 (例如: \"1 2 3\")"
    echo "  -d, --duration SECONDS       每个场景的测试持续时间 (默认: 60秒)"
    echo "  -h, --server HOST            服务器地址 (默认: 127.0.0.1)"
    echo "  -p, --port PORT              服务器端口 (默认: 8080)"
    echo "  --skip-build                 跳过编译步骤"
    echo "  --skip-analysis              跳过结果分析"
    echo "  --help                       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 运行所有测试场景"
    echo "  $0 -s \"1 2 3\" -d 30          # 运行场景1-3，每个30秒"
    echo "  $0 -h ************* -p 9090  # 连接到指定服务器"
    echo "  $0 --skip-build              # 跳过编译，直接测试"
}

# 主函数
main() {
    local scenarios=""
    local duration=""
    local server_addr=""
    local server_port=""
    local skip_build=false
    local skip_analysis=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--scenarios)
                scenarios="$2"
                shift 2
                ;;
            -d|--duration)
                duration="$2"
                shift 2
                ;;
            -h|--server)
                server_addr="$2"
                shift 2
                ;;
            -p|--port)
                server_port="$2"
                shift 2
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-analysis)
                skip_analysis=true
                SKIP_ANALYSIS=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示启动信息
    echo "======================================"
    echo "TCP压力测试快速启动脚本"
    echo "======================================"
    echo ""
    
    # 执行步骤
    check_environment
    
    if [ "$skip_build" = false ]; then
        build_program
    else
        log_info "跳过编译步骤"
        if [ ! -f "tcp_qt5" ]; then
            log_error "可执行文件 tcp_qt5 不存在，请先编译程序"
            exit 1
        fi
    fi
    
    check_server "$server_addr" "$server_port"
    
    if [ "$skip_analysis" = false ]; then
        install_python_deps
    fi
    
    run_test "$scenarios" "$duration" "$server_addr" "$server_port"
    
    if [ "$skip_analysis" = false ]; then
        analyze_results
    fi
    
    echo ""
    log_success "所有步骤完成！"
    
    # 显示结果位置
    if [ -d "test_results" ]; then
        echo ""
        log_info "测试结果位置:"
        echo "  - 原始日志: test_results/"
        if [ -d "analysis_output" ]; then
            echo "  - 分析报告: analysis_output/"
        fi
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi