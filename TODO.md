# TCP压力测试方案 - 待办事项

## 🔧 必须完成的配置

### 1. 修改MainWindow类以支持新的参数

**当前状态**: ❌ 未完成  
**优先级**: 高

需要在 `mainwindow.cpp` 中实现以下功能：

```cpp
// 在MainWindow构造函数或初始化方法中添加
void MainWindow::applyConfiguration() {
    // 根据设置的参数配置TcpShmClientWrapper
    if (!m_configFile.isEmpty()) {
        // 读取配置文件并应用设置
        loadConfigFile(m_configFile);
    }
    
    // 应用服务器地址和端口
    if (!m_serverAddress.isEmpty() && m_serverPort > 0) {
        // 配置连接参数
        configureConnection(m_serverAddress, m_serverPort);
    }
    
    // 应用发送间隔
    if (m_sendInterval > 0) {
        // 配置发送间隔
        configureSendInterval(m_sendInterval);
    }
}
```

### 2. 实现配置文件读取功能

**当前状态**: ❌ 未完成  
**优先级**: 高

需要添加YAML配置文件读取功能：

```cpp
// 在mainwindow.h中添加
#include <QSettings>
// 或者如果使用YAML库
// #include "yaml-cpp/yaml.h"

private:
    void loadConfigFile(const QString& configPath);
    void configureConnection(const QString& address, int port);
    void configureSendInterval(int interval);
```

### 3. 集成TcpShmClientWrapper的配置接口

**当前状态**: ❌ 未完成  
**优先级**: 高

检查 `TcpShmClientWrapper` 类是否提供了以下接口：
- 设置服务器地址和端口
- 设置消息发送间隔
- 设置消息大小
- 启用/禁用统计功能

如果没有，需要添加这些接口。

### 4. 添加无界面模式支持

**当前状态**: ❌ 未完成  
**优先级**: 中

在无界面模式下，程序应该：
- 不显示GUI窗口
- 自动开始连接和发送消息
- 在指定时间后自动退出
- 输出必要的日志信息

```cpp
// 在main.cpp中已添加基础框架，需要在MainWindow中实现
void MainWindow::runHeadlessMode(int duration) {
    // 隐藏窗口
    hide();
    
    // 开始测试
    startTest();
    
    // 设置定时器，在指定时间后退出
    QTimer::singleShot(duration * 1000, this, [this]() {
        stopTest();
        QApplication::quit();
    });
}
```

## 📋 可选的改进项

### 5. 添加日志输出功能

**当前状态**: ❌ 未完成  
**优先级**: 中

实现结构化的日志输出：
- 连接状态日志
- 消息发送/接收统计
- 错误和异常日志
- 性能指标日志

### 6. 实现统计数据收集

**当前状态**: ❌ 未完成  
**优先级**: 中

收集以下统计数据：
- 发送消息总数
- 成功/失败消息数
- 平均响应时间
- 连接建立/断开次数

### 7. 添加配置验证

**当前状态**: ❌ 未完成  
**优先级**: 低

验证配置文件的有效性：
- 检查必需参数是否存在
- 验证参数值的合理性
- 提供友好的错误提示

## 🔍 需要检查的现有代码

### 8. TcpShmClientWrapper类接口

**当前状态**: ❓ 需要检查  
**优先级**: 高

需要查看 `tcpshmclientwrapper.h` 和 `tcpshmclientwrapper.cpp`，确认：
- 现有的公共接口
- 配置方法
- 回调机制
- 统计功能

### 9. 现有的网络通信实现

**当前状态**: ❓ 需要检查  
**优先级**: 高

检查网络模块中的TCP客户端实现：
- `PsiTcpShmClient.h/cpp`
- 连接管理机制
- 消息发送机制
- 错误处理机制

## 🚀 快速开始步骤

### 立即可以做的事情：

1. **测试现有编译**
   ```bash
   cd /Users/<USER>/Desktop/cui/tcp_qt5
   qmake
   make
   ```

2. **检查TcpShmClientWrapper接口**
   ```bash
   # 查看头文件
   cat src/tcpshmclientwrapper.h
   ```

3. **运行快速测试脚本**
   ```bash
   chmod +x quick_test.sh
   ./quick_test.sh --help
   ```

4. **手动测试单个客户端**
   ```bash
   # 编译成功后
   ./tcp_qt5 --help
   ```

## ⚠️ 注意事项

1. **Qt版本兼容性**: 确保使用的Qt版本支持所需的功能
2. **线程安全**: 如果涉及多线程，注意线程安全问题
3. **资源管理**: 确保正确释放网络连接和内存资源
4. **错误处理**: 添加充分的错误处理和异常捕获

## 📞 需要澄清的问题

1. **TcpShmClientWrapper的具体功能**: 这个类目前实现了哪些功能？
2. **网络协议细节**: TCP通信使用的具体协议格式是什么？
3. **消息格式**: 发送的消息格式和内容是什么？
4. **服务器要求**: 测试的TCP服务器有什么特殊要求？
5. **性能目标**: 期望达到的性能指标是什么？

---

**建议的实施顺序**:
1. 先检查现有代码和接口 (项目8、9)
2. 实现基础的参数配置功能 (项目1、2、3)
3. 添加无界面模式支持 (项目4)
4. 测试基本功能
5. 添加日志和统计功能 (项目5、6)
6. 完善和优化 (项目7)